[{"D:\\学习\\my-vue2-project\\src\\main.js": "1", "D:\\学习\\my-vue2-project\\src\\App.vue": "2", "D:\\学习\\my-vue2-project\\src\\components\\PageHeader.vue": "3", "D:\\学习\\my-vue2-project\\src\\components\\common\\TextButton.vue": "4", "D:\\学习\\my-vue2-project\\src\\router\\index.js": "5", "D:\\学习\\my-vue2-project\\src\\store\\index.js": "6", "D:\\学习\\my-vue2-project\\src\\views\\NoteList.vue": "7", "D:\\学习\\my-vue2-project\\src\\views\\ReadingHistory.vue": "8", "D:\\学习\\my-vue2-project\\src\\views\\Multimodal.vue": "9", "D:\\学习\\my-vue2-project\\src\\views\\Home.vue": "10", "D:\\学习\\my-vue2-project\\src\\views\\LearningMonitoring.vue": "11", "D:\\学习\\my-vue2-project\\src\\views\\FloatingWindow.vue": "12", "D:\\学习\\my-vue2-project\\src\\services\\api.js": "13", "D:\\学习\\my-vue2-project\\src\\mixins\\windowAdaptive.js": "14", "D:\\学习\\my-vue2-project\\src\\components\\common\\SubMenuItem.vue": "15", "D:\\学习\\my-vue2-project\\src\\views\\HtmlViewer.vue": "16", "D:\\学习\\my-vue2-project\\src\\services\\floatingWindowService.js": "17", "D:\\学习\\my-vue2-project\\src\\services\\applicationMonitor.js": "18"}, {"size": 624, "mtime": 1752407202459, "results": "19", "hashOfConfig": "20"}, {"size": 4530, "mtime": 1752593805098, "results": "21", "hashOfConfig": "20"}, {"size": 1857, "mtime": 1752413493816, "results": "22", "hashOfConfig": "20"}, {"size": 4070, "mtime": 1752392939913, "results": "23", "hashOfConfig": "20"}, {"size": 1095, "mtime": 1753538529748, "results": "24", "hashOfConfig": "20"}, {"size": 6096, "mtime": 1753197041119, "results": "25", "hashOfConfig": "20"}, {"size": 61632, "mtime": 1753635968314, "results": "26", "hashOfConfig": "20"}, {"size": 7452, "mtime": 1753196009180, "results": "27", "hashOfConfig": "20"}, {"size": 1887, "mtime": 1752403754204, "results": "28", "hashOfConfig": "20"}, {"size": 18079, "mtime": 1753538529925, "results": "29", "hashOfConfig": "20"}, {"size": 41561, "mtime": 1752594298304, "results": "30", "hashOfConfig": "20"}, {"size": 3787, "mtime": 1752593805118, "results": "31", "hashOfConfig": "20"}, {"size": 859, "mtime": 1752455028435, "results": "32", "hashOfConfig": "20"}, {"size": 5067, "mtime": 1752591047108, "results": "33", "hashOfConfig": "20"}, {"size": 643, "mtime": 1752425400616, "results": "34", "hashOfConfig": "20"}, {"size": 556, "mtime": 1752427265554, "results": "35", "hashOfConfig": "20"}, {"size": 6977, "mtime": 1752592031468, "results": "36", "hashOfConfig": "20"}, {"size": 8798, "mtime": 1752594679104, "results": "37", "hashOfConfig": "20"}, {"filePath": "38", "messages": "39", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "6rl6kf", {"filePath": "40", "messages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "42", "messages": "43", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "44", "messages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "46", "messages": "47", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "48", "messages": "49", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "50", "messages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "52", "messages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "54", "messages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "56", "messages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "58", "messages": "59", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "60", "messages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "62", "messages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "66", "messages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "68", "messages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "70", "messages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "72", "messages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\学习\\my-vue2-project\\src\\main.js", [], "D:\\学习\\my-vue2-project\\src\\App.vue", [], "D:\\学习\\my-vue2-project\\src\\components\\PageHeader.vue", [], "D:\\学习\\my-vue2-project\\src\\components\\common\\TextButton.vue", [], "D:\\学习\\my-vue2-project\\src\\router\\index.js", [], "D:\\学习\\my-vue2-project\\src\\store\\index.js", [], "D:\\学习\\my-vue2-project\\src\\views\\NoteList.vue", [], "D:\\学习\\my-vue2-project\\src\\views\\ReadingHistory.vue", [], "D:\\学习\\my-vue2-project\\src\\views\\Multimodal.vue", [], "D:\\学习\\my-vue2-project\\src\\views\\Home.vue", [], "D:\\学习\\my-vue2-project\\src\\views\\LearningMonitoring.vue", [], "D:\\学习\\my-vue2-project\\src\\views\\FloatingWindow.vue", [], "D:\\学习\\my-vue2-project\\src\\services\\api.js", [], "D:\\学习\\my-vue2-project\\src\\mixins\\windowAdaptive.js", [], "D:\\学习\\my-vue2-project\\src\\components\\common\\SubMenuItem.vue", [], "D:\\学习\\my-vue2-project\\src\\views\\HtmlViewer.vue", [], "D:\\学习\\my-vue2-project\\src\\services\\floatingWindowService.js", [], "D:\\学习\\my-vue2-project\\src\\services\\applicationMonitor.js", []]