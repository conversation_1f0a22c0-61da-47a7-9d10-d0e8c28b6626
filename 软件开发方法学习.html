<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>软件开发方法 - 交互式学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            opacity: 0;
            animation: fadeInUp 1s ease-out forwards;
        }

        .title {
            font-size: 3rem;
            color: white;
            margin-bottom: 20px;
            text-shadow: 0 4px 8px rgba(0,0,0,0.3);
        }

        .subtitle {
            font-size: 1.2rem;
            color: rgba(255,255,255,0.8);
            margin-bottom: 40px;
        }

        .question-card {
            background: white;
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            opacity: 0;
            animation: fadeInUp 1s ease-out 0.3s forwards;
        }

        .question-title {
            font-size: 1.5rem;
            color: #333;
            margin-bottom: 30px;
            line-height: 1.6;
        }

        .options {
            display: grid;
            gap: 15px;
            margin-bottom: 30px;
        }

        .option {
            padding: 20px;
            border: 2px solid #e0e0e0;
            border-radius: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
            background: #f8f9fa;
            position: relative;
            overflow: hidden;
        }

        .option:hover {
            border-color: #667eea;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.15);
        }

        .option.selected {
            border-color: #667eea;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
        }

        .option.correct {
            border-color: #4CAF50;
            background: linear-gradient(135deg, #4CAF50, #45a049);
            color: white;
        }

        .canvas-container {
            background: white;
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            opacity: 0;
            animation: fadeInUp 1s ease-out 0.6s forwards;
        }

        canvas {
            width: 100%;
            height: 400px;
            border-radius: 15px;
            background: #f8f9fa;
        }

        .explanation {
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            opacity: 0;
            animation: fadeInUp 1s ease-out 0.9s forwards;
        }

        .method-card {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            padding: 30px;
            border-radius: 15px;
            margin: 20px 0;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .method-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(240, 147, 251, 0.4);
        }

        .method-card.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1.1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 10px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .highlight {
            background: linear-gradient(135deg, #FFD700, #FFA500);
            color: #333;
            padding: 5px 10px;
            border-radius: 8px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">软件开发方法学习</h1>
            <p class="subtitle">通过动画和交互理解不同的软件开发方法</p>
        </div>

        <div class="question-card">
            <h2 class="question-title">
                某开发组在开发某个系统时，各个阶段具有严格的界限，只有一个阶段的获得认可才能进行下一个阶段的工作，则该开发组最可能采用的软件开发方法是（ ）。
            </h2>
            
            <div class="options">
                <div class="option" data-option="A">
                    <strong>A. 构件化方法</strong>
                </div>
                <div class="option" data-option="B">
                    <strong>B. 结构化方法</strong>
                </div>
                <div class="option" data-option="C">
                    <strong>C. 面向对象方法</strong>
                </div>
                <div class="option" data-option="D">
                    <strong>D. 快速原型法</strong>
                </div>
            </div>

            <button class="btn" onclick="checkAnswer()">提交答案</button>
            <button class="btn" onclick="showExplanation()">查看解析</button>
        </div>

        <div class="canvas-container">
            <h3 style="margin-bottom: 20px; color: #333;">软件开发方法动画演示</h3>
            <canvas id="methodCanvas"></canvas>
            <div style="text-align: center; margin-top: 20px;">
                <button class="btn" onclick="animateStructured()">结构化方法</button>
                <button class="btn" onclick="animateComponent()">构件化方法</button>
                <button class="btn" onclick="animateOOP()">面向对象方法</button>
                <button class="btn" onclick="animatePrototype()">快速原型法</button>
            </div>
        </div>

        <div class="explanation" id="explanation" style="display: none;">
            <h3 style="color: #333; margin-bottom: 30px;">📚 知识点详解</h3>
            
            <div class="method-card" onclick="toggleMethod('structured')">
                <h4>🏗️ 结构化方法（瀑布模型）</h4>
                <div id="structured-detail" style="display: none; margin-top: 15px;">
                    <p><span class="highlight">核心特点</span>：各阶段严格按顺序进行，前一阶段完成并通过验收后才能进入下一阶段</p>
                    <p><strong>阶段顺序</strong>：需求分析 → 系统设计 → 编码实现 → 测试 → 维护</p>
                    <p><strong>优点</strong>：文档完整、过程可控、质量有保证</p>
                    <p><strong>缺点</strong>：灵活性差、周期长、难以适应需求变化</p>
                </div>
            </div>

            <div class="method-card" onclick="toggleMethod('component')">
                <h4>🧩 构件化方法</h4>
                <div id="component-detail" style="display: none; margin-top: 15px;">
                    <p><span class="highlight">核心特点</span>：基于可重用构件进行软件开发</p>
                    <p><strong>开发方式</strong>：寻找现有构件 → 构件集成 → 定制开发</p>
                    <p><strong>优点</strong>：提高开发效率、降低成本、提高质量</p>
                    <p><strong>缺点</strong>：构件依赖性、集成复杂度高</p>
                </div>
            </div>

            <div class="method-card" onclick="toggleMethod('oop')">
                <h4>🎯 面向对象方法</h4>
                <div id="oop-detail" style="display: none; margin-top: 15px;">
                    <p><span class="highlight">核心特点</span>：以对象为中心进行分析、设计和实现</p>
                    <p><strong>核心概念</strong>：封装、继承、多态</p>
                    <p><strong>优点</strong>：代码重用性好、维护性强、扩展性好</p>
                    <p><strong>缺点</strong>：学习成本高、设计复杂度增加</p>
                </div>
            </div>

            <div class="method-card" onclick="toggleMethod('prototype')">
                <h4>⚡ 快速原型法</h4>
                <div id="prototype-detail" style="display: none; margin-top: 15px;">
                    <p><span class="highlight">核心特点</span>：快速构建原型，通过用户反馈不断改进</p>
                    <p><strong>开发流程</strong>：需求获取 → 快速设计 → 构建原型 → 用户评估 → 改进原型</p>
                    <p><strong>优点</strong>：用户参与度高、需求理解准确、风险降低</p>
                    <p><strong>缺点</strong>：可能导致系统结构不清晰</p>
                </div>
            </div>

            <div style="margin-top: 40px; padding: 30px; background: linear-gradient(135deg, #4CAF50, #45a049); border-radius: 15px; color: white;">
                <h4>💡 解题思路</h4>
                <p style="margin: 10px 0;">题目关键词：<span style="background: rgba(255,255,255,0.2); padding: 3px 8px; border-radius: 5px;">"各个阶段具有严格的界限"</span></p>
                <p style="margin: 10px 0;">题目关键词：<span style="background: rgba(255,255,255,0.2); padding: 3px 8px; border-radius: 5px;">"只有一个阶段获得认可才能进行下一个阶段"</span></p>
                <p style="margin: 10px 0;"><strong>分析</strong>：这明显描述的是瀑布模型的特征，即结构化方法的典型特点</p>
                <p style="margin: 10px 0;"><strong>答案</strong>：B. 结构化方法</p>
            </div>
        </div>
    </div>

    <script>
        let selectedOption = null;
        const canvas = document.getElementById('methodCanvas');
        const ctx = canvas.getContext('2d');
        
        // 设置canvas尺寸
        function resizeCanvas() {
            const rect = canvas.getBoundingClientRect();
            canvas.width = rect.width * window.devicePixelRatio;
            canvas.height = rect.height * window.devicePixelRatio;
            ctx.scale(window.devicePixelRatio, window.devicePixelRatio);
        }
        
        resizeCanvas();
        window.addEventListener('resize', resizeCanvas);

        // 选项点击事件
        document.querySelectorAll('.option').forEach(option => {
            option.addEventListener('click', function() {
                document.querySelectorAll('.option').forEach(opt => opt.classList.remove('selected'));
                this.classList.add('selected');
                selectedOption = this.dataset.option;
            });
        });

        function checkAnswer() {
            if (!selectedOption) {
                alert('请先选择一个答案！');
                return;
            }
            
            document.querySelectorAll('.option').forEach(opt => {
                if (opt.dataset.option === 'B') {
                    opt.classList.add('correct');
                }
            });
            
            if (selectedOption === 'B') {
                alert('🎉 恭喜你答对了！结构化方法确实具有严格的阶段界限特征。');
            } else {
                alert('❌ 答案不正确。正确答案是B：结构化方法。请查看解析了解详情。');
            }
        }

        function showExplanation() {
            document.getElementById('explanation').style.display = 'block';
            document.getElementById('explanation').scrollIntoView({ behavior: 'smooth' });
        }

        function toggleMethod(methodId) {
            const detail = document.getElementById(methodId + '-detail');
            const card = detail.parentElement;
            
            if (detail.style.display === 'none') {
                // 关闭其他所有详情
                document.querySelectorAll('[id$="-detail"]').forEach(d => {
                    d.style.display = 'none';
                    d.parentElement.classList.remove('active');
                });
                
                detail.style.display = 'block';
                card.classList.add('active');
            } else {
                detail.style.display = 'none';
                card.classList.remove('active');
            }
        }

        // 动画函数
        function clearCanvas() {
            ctx.clearRect(0, 0, canvas.width / window.devicePixelRatio, canvas.height / window.devicePixelRatio);
        }

        function animateStructured() {
            clearCanvas();
            const stages = ['需求分析', '系统设计', '编码实现', '测试', '维护'];
            const colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7'];
            
            let currentStage = 0;
            
            function drawStage() {
                if (currentStage >= stages.length) return;
                
                const x = 50 + currentStage * 150;
                const y = 150;
                
                // 绘制阶段框
                ctx.fillStyle = colors[currentStage];
                ctx.fillRect(x, y, 120, 80);
                
                // 绘制文字
                ctx.fillStyle = 'white';
                ctx.font = '14px Microsoft YaHei';
                ctx.textAlign = 'center';
                ctx.fillText(stages[currentStage], x + 60, y + 45);
                
                // 绘制箭头
                if (currentStage < stages.length - 1) {
                    ctx.strokeStyle = '#333';
                    ctx.lineWidth = 3;
                    ctx.beginPath();
                    ctx.moveTo(x + 120, y + 40);
                    ctx.lineTo(x + 140, y + 40);
                    ctx.moveTo(x + 135, y + 35);
                    ctx.lineTo(x + 140, y + 40);
                    ctx.lineTo(x + 135, y + 45);
                    ctx.stroke();
                }
                
                currentStage++;
                setTimeout(drawStage, 800);
            }
            
            drawStage();
        }

        function animateComponent() {
            clearCanvas();
            // 绘制构件化方法动画
            const components = ['构件A', '构件B', '构件C'];
            const colors = ['#FF6B6B', '#4ECDC4', '#45B7D1'];
            
            components.forEach((comp, index) => {
                setTimeout(() => {
                    const x = 100 + index * 100;
                    const y = 100;
                    
                    ctx.fillStyle = colors[index];
                    ctx.fillRect(x, y, 80, 60);
                    ctx.fillStyle = 'white';
                    ctx.font = '12px Microsoft YaHei';
                    ctx.textAlign = 'center';
                    ctx.fillText(comp, x + 40, y + 35);
                    
                    // 组装箭头
                    if (index === components.length - 1) {
                        setTimeout(() => {
                            ctx.strokeStyle = '#333';
                            ctx.lineWidth = 2;
                            ctx.beginPath();
                            ctx.moveTo(150, 200);
                            ctx.lineTo(250, 200);
                            ctx.stroke();
                            
                            ctx.fillStyle = '#96CEB4';
                            ctx.fillRect(200, 220, 100, 60);
                            ctx.fillStyle = 'white';
                            ctx.fillText('集成系统', 250, 255);
                        }, 500);
                    }
                }, index * 600);
            });
        }

        function animateOOP() {
            clearCanvas();
            // 绘制面向对象方法动画
            const objects = ['用户类', '订单类', '商品类'];
            const colors = ['#FF6B6B', '#4ECDC4', '#45B7D1'];
            
            objects.forEach((obj, index) => {
                setTimeout(() => {
                    const x = 50 + index * 120;
                    const y = 100;
                    
                    // 绘制类
                    ctx.fillStyle = colors[index];
                    ctx.fillRect(x, y, 100, 120);
                    
                    // 类名
                    ctx.fillStyle = 'white';
                    ctx.font = 'bold 14px Microsoft YaHei';
                    ctx.textAlign = 'center';
                    ctx.fillText(obj, x + 50, y + 25);
                    
                    // 属性和方法
                    ctx.font = '10px Microsoft YaHei';
                    ctx.fillText('属性1', x + 50, y + 50);
                    ctx.fillText('属性2', x + 50, y + 65);
                    ctx.fillText('方法1()', x + 50, y + 85);
                    ctx.fillText('方法2()', x + 50, y + 100);
                    
                    // 绘制关联线
                    if (index > 0) {
                        ctx.strokeStyle = '#333';
                        ctx.lineWidth = 2;
                        ctx.beginPath();
                        ctx.moveTo(x, y + 60);
                        ctx.lineTo(x - 20, y + 60);
                        ctx.stroke();
                    }
                }, index * 700);
            });
        }

        function animatePrototype() {
            clearCanvas();
            // 绘制快速原型法动画
            let iteration = 0;
            const maxIterations = 3;
            
            function drawIteration() {
                if (iteration >= maxIterations) return;
                
                const y = 50 + iteration * 100;
                
                // 原型框
                ctx.fillStyle = iteration === 0 ? '#FF6B6B' : iteration === 1 ? '#4ECDC4' : '#45B7D1';
                ctx.fillRect(50, y, 100, 60);
                
                ctx.fillStyle = 'white';
                ctx.font = '12px Microsoft YaHei';
                ctx.textAlign = 'center';
                ctx.fillText(`原型 v${iteration + 1}`, 100, y + 35);
                
                // 用户反馈
                ctx.fillStyle = '#FFEAA7';
                ctx.fillRect(200, y, 100, 60);
                ctx.fillStyle = '#333';
                ctx.fillText('用户反馈', 250, y + 35);
                
                // 箭头
                ctx.strokeStyle = '#333';
                ctx.lineWidth = 2;
                ctx.beginPath();
                ctx.moveTo(150, y + 30);
                ctx.lineTo(200, y + 30);
                ctx.stroke();
                
                if (iteration < maxIterations - 1) {
                    // 改进箭头
                    ctx.beginPath();
                    ctx.moveTo(250, y + 60);
                    ctx.lineTo(100, y + 90);
                    ctx.stroke();
                }
                
                iteration++;
                setTimeout(drawIteration, 1500);
            }
            
            drawIteration();
        }

        // 初始化显示结构化方法动画
        setTimeout(() => {
            animateStructured();
        }, 1000);
    </script>
</body>
</html>
