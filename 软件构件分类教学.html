<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>软件构件分类 - 交互式学习</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .section {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 40px;
            margin: 30px 0;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            opacity: 0;
            transform: translateY(50px);
        }

        .question-section {
            text-align: center;
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            color: white;
        }

        .question-title {
            font-size: 2.5em;
            margin-bottom: 30px;
            font-weight: bold;
        }

        .question-content {
            font-size: 1.4em;
            line-height: 1.8;
            margin-bottom: 30px;
        }

        .options {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 20px;
            margin: 30px 0;
        }

        .option {
            background: rgba(255, 255, 255, 0.2);
            padding: 20px;
            border-radius: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 3px solid transparent;
        }

        .option:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-5px);
        }

        .canvas-container {
            display: flex;
            justify-content: center;
            margin: 30px 0;
        }

        canvas {
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        }

        .interactive-area {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 30px;
            margin: 20px 0;
            text-align: center;
        }

        .component-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin: 15px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-block;
            min-width: 200px;
        }

        .component-card:hover {
            transform: translateY(-10px) scale(1.05);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
        }

        .component-title {
            font-size: 1.3em;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 10px;
        }

        .component-desc {
            font-size: 1em;
            color: #7f8c8d;
            line-height: 1.6;
        }

        .highlight {
            background: linear-gradient(120deg, #a8edea 0%, #fed6e3 100%);
            padding: 3px 8px;
            border-radius: 5px;
            font-weight: bold;
        }

        .next-btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 15px 40px;
            border-radius: 50px;
            font-size: 1.2em;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 20px 10px;
        }

        .next-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
        }

        .progress-bar {
            height: 6px;
            background: #e0e0e0;
            border-radius: 3px;
            margin: 20px 0;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            width: 0%;
            transition: width 0.5s ease;
        }

        .step-indicator {
            text-align: center;
            margin: 20px 0;
            font-size: 1.1em;
            color: #666;
        }

        .drag-area {
            display: flex;
            justify-content: space-between;
            margin: 30px 0;
            gap: 30px;
        }

        .drag-items, .drop-zones {
            flex: 1;
        }

        .drag-item {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 15px;
            margin: 10px 0;
            border-radius: 10px;
            cursor: grab;
            text-align: center;
            transition: all 0.3s ease;
            user-select: none;
        }

        .drag-item:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
        }

        .drag-item:active {
            cursor: grabbing;
        }

        .drop-zone {
            background: #f8f9fa;
            border: 2px dashed #dee2e6;
            padding: 20px;
            margin: 10px 0;
            border-radius: 10px;
            text-align: center;
            min-height: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
        }

        .drop-zone:hover {
            border-color: #667eea;
            background: #f0f8ff;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 题目展示区 -->
        <div class="section question-section" id="question-section">
            <h1 class="question-title">软件构件分类学习</h1>
            <div class="question-content">
                <p><strong>题目：</strong>按照外部形态，构成一个软件系统的构件可以分为五类，其中，（ ）是指可以进行版本替换并增加构件新功能。</p>
                <div class="options">
                    <div class="option" data-answer="A">A. 装配的构件</div>
                    <div class="option" data-answer="B">B. 可修改的构件</div>
                    <div class="option" data-answer="C">C. 有限制的构件</div>
                    <div class="option" data-answer="D">D. 适应性构件</div>
                </div>
                <p><strong>正确答案：B</strong></p>
            </div>
            <button class="next-btn" onclick="startLearning()">开始学习 🚀</button>
        </div>

        <!-- 进度条 -->
        <div class="progress-bar">
            <div class="progress-fill" id="progress-fill"></div>
        </div>
        <div class="step-indicator" id="step-indicator">第 1 步 / 6 步</div>

        <!-- 基础概念介绍 -->
        <div class="section" id="concept-section">
            <h2>🏗️ 什么是软件构件？</h2>
            <div class="canvas-container">
                <canvas id="concept-canvas" width="800" height="400"></canvas>
            </div>
            <div class="interactive-area">
                <p style="font-size: 1.3em; margin-bottom: 20px;">
                    想象软件系统就像<span class="highlight">搭积木</span>一样，每个积木块就是一个<span class="highlight">构件</span>！
                </p>
                <button class="next-btn" onclick="showComponentTypes()">了解构件分类 📚</button>
            </div>
        </div>

        <!-- 五类构件展示 -->
        <div class="section" id="types-section">
            <h2>🎯 软件构件的五大类型</h2>
            <div class="canvas-container">
                <canvas id="types-canvas" width="1000" height="500"></canvas>
            </div>
            <div class="interactive-area">
                <p style="font-size: 1.2em; margin-bottom: 30px;">点击下面的构件卡片，了解每种类型的特点：</p>
                <div id="component-cards">
                    <div class="component-card" data-type="independent">
                        <div class="component-title">🔒 独立而成熟的构件</div>
                        <div class="component-desc">完全封闭，只能按规定使用</div>
                    </div>
                    <div class="component-card" data-type="restricted">
                        <div class="component-title">⚠️ 有限制的构件</div>
                        <div class="component-desc">有接口，但使用时需要测试</div>
                    </div>
                    <div class="component-card" data-type="adaptive">
                        <div class="component-title">🔄 适应性构件</div>
                        <div class="component-desc">经过包装，可直接使用</div>
                    </div>
                    <div class="component-card" data-type="assembled">
                        <div class="component-title">🔧 装配的构件</div>
                        <div class="component-desc">已装配好，用胶水代码连接</div>
                    </div>
                    <div class="component-card" data-type="modifiable">
                        <div class="component-title">✏️ 可修改的构件</div>
                        <div class="component-desc">可以版本替换和增加功能</div>
                    </div>
                </div>
                <button class="next-btn" onclick="showDetailedExplanation()">深入理解 🔍</button>
            </div>
        </div>

        <!-- 详细解释区域 -->
        <div class="section" id="detailed-section">
            <h2>🔍 深入理解：可修改的构件</h2>
            <div class="canvas-container">
                <canvas id="detailed-canvas" width="900" height="400"></canvas>
            </div>
            <div class="interactive-area">
                <h3>🎯 关键特征分析</h3>
                <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 20px; margin: 20px 0;">
                    <div style="background: #e8f5e8; padding: 20px; border-radius: 10px;">
                        <h4>✅ 版本替换</h4>
                        <p>可以用新版本替换旧版本，保持系统功能的连续性</p>
                    </div>
                    <div style="background: #e8f5e8; padding: 20px; border-radius: 10px;">
                        <h4>✅ 增加新功能</h4>
                        <p>通过重新"包装"或写接口来实现功能扩展</p>
                    </div>
                </div>
                <p style="font-size: 1.2em; color: #2c3e50; margin: 20px 0;">
                    <span class="highlight">可修改的构件</span>是应用系统开发中使用最多的构件类型！
                </p>
                <button class="next-btn" onclick="showSolutionProcess()">解题思路 💡</button>
            </div>
        </div>

        <!-- 解题过程区域 -->
        <div class="section" id="solution-section">
            <h2>💡 解题思路分析</h2>
            <div class="canvas-container">
                <canvas id="solution-canvas" width="800" height="300"></canvas>
            </div>
            <div class="interactive-area">
                <h3>🔑 关键词识别</h3>
                <div style="background: #fff3cd; padding: 20px; border-radius: 10px; margin: 20px 0;">
                    <p style="font-size: 1.3em; text-align: center;">
                        题目关键词：<span class="highlight">"版本替换"</span> + <span class="highlight">"增加构件新功能"</span>
                    </p>
                </div>
                <h3>📋 解题步骤</h3>
                <ol style="font-size: 1.1em; line-height: 2; margin: 20px 0;">
                    <li>找出题目中的关键特征描述</li>
                    <li>回忆五种构件类型的定义</li>
                    <li>逐一对比各选项特点</li>
                    <li>选择最匹配的答案</li>
                </ol>
                <button class="next-btn" onclick="showComparison()">选项对比 ⚖️</button>
            </div>
        </div>

        <!-- 选项对比区域 -->
        <div class="section" id="comparison-section">
            <h2>⚖️ 选项对比分析</h2>
            <div class="canvas-container">
                <canvas id="comparison-canvas" width="700" height="350"></canvas>
            </div>
            <div class="interactive-area">
                <h3>🎮 互动练习</h3>
                <div id="quiz-container"></div>
                <button class="next-btn" onclick="startInteractiveQuiz()">开始练习 🎯</button>
                <button class="next-btn" onclick="showFinalSummary()" style="background: linear-gradient(135deg, #27ae60, #2ecc71);">完成学习 🎉</button>
            </div>
        </div>

        <!-- 总结区域 -->
        <div class="section" id="summary-section">
            <h2>🎉 学习总结</h2>
            <div class="interactive-area">
                <h3>📚 知识要点回顾</h3>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin: 20px 0;">
                    <div style="background: #f8f9fa; padding: 15px; border-radius: 10px; border-left: 4px solid #e74c3c;">
                        <strong>独立成熟</strong><br>完全封闭，按规定使用
                    </div>
                    <div style="background: #f8f9fa; padding: 15px; border-radius: 10px; border-left: 4px solid #f39c12;">
                        <strong>有限制</strong><br>有接口，需要测试
                    </div>
                    <div style="background: #f8f9fa; padding: 15px; border-radius: 10px; border-left: 4px solid #27ae60;">
                        <strong>适应性</strong><br>包装处理，直接使用
                    </div>
                    <div style="background: #f8f9fa; padding: 15px; border-radius: 10px; border-left: 4px solid #9b59b6;">
                        <strong>装配的</strong><br>胶水代码连接
                    </div>
                    <div style="background: #e8f5e8; padding: 15px; border-radius: 10px; border-left: 4px solid #2ecc71; border: 2px solid #27ae60;">
                        <strong>可修改</strong><br>版本替换+增加功能 ✓
                    </div>
                </div>
                <div style="background: linear-gradient(135deg, #667eea, #764ba2); color: white; padding: 30px; border-radius: 15px; text-align: center; margin: 30px 0;">
                    <h3>🏆 恭喜完成学习！</h3>
                    <p style="font-size: 1.2em; margin: 15px 0;">
                        您已经掌握了软件构件分类的核心知识点！<br>
                        <strong>可修改的构件</strong>因其<span style="background: rgba(255,255,255,0.2); padding: 2px 8px; border-radius: 5px;">版本替换</span>和<span style="background: rgba(255,255,255,0.2); padding: 2px 8px; border-radius: 5px;">功能扩展</span>特性成为正确答案。
                    </p>
                </div>
            </div>
        </div>
    </div>

    <script>
        let currentStep = 1;
        const totalSteps = 6;

        // 更新进度
        function updateProgress() {
            const progress = (currentStep / totalSteps) * 100;
            document.getElementById('progress-fill').style.width = progress + '%';
            document.getElementById('step-indicator').textContent = `第 ${currentStep} 步 / ${totalSteps} 步`;
        }

        // 显示动画
        function showSection(sectionId) {
            const section = document.getElementById(sectionId);
            gsap.to(section, {
                opacity: 1,
                y: 0,
                duration: 0.8,
                ease: "power2.out"
            });
        }

        // 开始学习
        function startLearning() {
            currentStep = 2;
            updateProgress();
            showSection('concept-section');
            drawConceptAnimation();
            
            // 滚动到概念区域
            document.getElementById('concept-section').scrollIntoView({
                behavior: 'smooth'
            });
        }

        // 绘制概念动画
        function drawConceptAnimation() {
            const canvas = document.getElementById('concept-canvas');
            const ctx = canvas.getContext('2d');
            
            // 清空画布
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 绘制软件系统框架
            ctx.strokeStyle = '#3498db';
            ctx.lineWidth = 3;
            ctx.strokeRect(50, 50, 700, 300);
            
            // 标题
            ctx.fillStyle = '#2c3e50';
            ctx.font = 'bold 24px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText('软件系统', 400, 30);
            
            // 绘制构件积木
            const components = [
                {x: 100, y: 100, color: '#e74c3c', label: '构件1'},
                {x: 250, y: 100, color: '#f39c12', label: '构件2'},
                {x: 400, y: 100, color: '#27ae60', label: '构件3'},
                {x: 550, y: 100, color: '#9b59b6', label: '构件4'},
                {x: 175, y: 200, color: '#34495e', label: '构件5'},
                {x: 325, y: 200, color: '#16a085', label: '构件6'},
                {x: 475, y: 200, color: '#e67e22', label: '构件7'}
            ];
            
            // 动画绘制构件
            components.forEach((comp, index) => {
                setTimeout(() => {
                    // 绘制构件方块
                    ctx.fillStyle = comp.color;
                    ctx.fillRect(comp.x, comp.y, 80, 60);
                    
                    // 绘制构件标签
                    ctx.fillStyle = 'white';
                    ctx.font = '14px Microsoft YaHei';
                    ctx.textAlign = 'center';
                    ctx.fillText(comp.label, comp.x + 40, comp.y + 35);
                    
                    // 添加闪烁效果
                    gsap.from(`#concept-canvas`, {
                        opacity: 0.7,
                        duration: 0.3,
                        yoyo: true,
                        repeat: 1
                    });
                }, index * 200);
            });
        }

        // 显示构件类型
        function showComponentTypes() {
            currentStep = 3;
            updateProgress();
            showSection('types-section');
            drawTypesAnimation();
            
            document.getElementById('types-section').scrollIntoView({
                behavior: 'smooth'
            });
        }

        // 绘制类型动画
        function drawTypesAnimation() {
            const canvas = document.getElementById('types-canvas');
            const ctx = canvas.getContext('2d');
            
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 绘制五种构件类型的可视化表示
            const types = [
                {x: 50, y: 100, color: '#e74c3c', label: '独立成熟', icon: '🔒'},
                {x: 250, y: 100, color: '#f39c12', label: '有限制', icon: '⚠️'},
                {x: 450, y: 100, color: '#27ae60', label: '适应性', icon: '🔄'},
                {x: 650, y: 100, color: '#9b59b6', label: '装配的', icon: '🔧'},
                {x: 850, y: 100, color: '#34495e', label: '可修改', icon: '✏️'}
            ];
            
            types.forEach((type, index) => {
                setTimeout(() => {
                    // 绘制圆形背景
                    ctx.beginPath();
                    ctx.arc(type.x + 50, type.y + 50, 40, 0, 2 * Math.PI);
                    ctx.fillStyle = type.color;
                    ctx.fill();
                    
                    // 绘制图标
                    ctx.fillStyle = 'white';
                    ctx.font = '30px Arial';
                    ctx.textAlign = 'center';
                    ctx.fillText(type.icon, type.x + 50, type.y + 60);
                    
                    // 绘制标签
                    ctx.fillStyle = '#2c3e50';
                    ctx.font = '16px Microsoft YaHei';
                    ctx.fillText(type.label, type.x + 50, type.y + 130);
                }, index * 300);
            });
        }

        // 构件卡片点击事件
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.component-card');
            cards.forEach(card => {
                card.addEventListener('click', function() {
                    const type = this.dataset.type;
                    highlightComponentType(type);
                    showComponentDetails(type);
                });
            });
        });

        function highlightComponentType(type) {
            // 高亮选中的卡片
            const cards = document.querySelectorAll('.component-card');
            cards.forEach(card => {
                if (card.dataset.type === type) {
                    gsap.to(card, {
                        scale: 1.1,
                        boxShadow: '0 20px 40px rgba(0, 0, 0, 0.3)',
                        duration: 0.3
                    });
                } else {
                    gsap.to(card, {
                        scale: 1,
                        boxShadow: '0 10px 25px rgba(0, 0, 0, 0.1)',
                        duration: 0.3
                    });
                }
            });
        }

        function showComponentDetails(type) {
            const details = {
                'independent': '独立而成熟的构件：如数据库管理系统、操作系统等，完全封闭，只能按规定命令使用。',
                'restricted': '有限制的构件：如基础类库，提供接口但有使用条件，装配时可能产生冲突。',
                'adaptive': '适应性构件：如ActiveX，经过包装处理，可在各种环境中直接使用。',
                'assembled': '装配的构件：已装配在系统不同层次上，用胶水代码连接使用。',
                'modifiable': '可修改的构件：可以进行版本替换，通过重新包装或写接口实现功能增加。'
            };
            
            // 显示详细说明
            alert(details[type]);
        }

        function showDetailedExplanation() {
            currentStep = 4;
            updateProgress();
            showSection('detailed-section');
            drawDetailedAnimation();

            document.getElementById('detailed-section').scrollIntoView({
                behavior: 'smooth'
            });
        }

        function drawDetailedAnimation() {
            const canvas = document.getElementById('detailed-canvas');
            const ctx = canvas.getContext('2d');

            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // 绘制可修改构件的特征图
            setTimeout(() => {
                // 绘制构件主体
                ctx.fillStyle = '#34495e';
                ctx.fillRect(350, 150, 200, 100);

                // 标题
                ctx.fillStyle = '#2c3e50';
                ctx.font = 'bold 20px Microsoft YaHei';
                ctx.textAlign = 'center';
                ctx.fillText('可修改的构件', 450, 130);

                // 版本替换箭头
                ctx.strokeStyle = '#e74c3c';
                ctx.lineWidth = 4;
                ctx.beginPath();
                ctx.moveTo(200, 200);
                ctx.lineTo(340, 200);
                ctx.stroke();

                // 版本标签
                ctx.fillStyle = '#e74c3c';
                ctx.font = '16px Microsoft YaHei';
                ctx.textAlign = 'center';
                ctx.fillText('版本替换', 270, 190);
                ctx.fillText('V1.0 → V2.0', 270, 220);

                // 功能扩展箭头
                ctx.strokeStyle = '#27ae60';
                ctx.beginPath();
                ctx.moveTo(560, 200);
                ctx.lineTo(700, 200);
                ctx.stroke();

                // 功能标签
                ctx.fillStyle = '#27ae60';
                ctx.fillText('功能扩展', 630, 190);
                ctx.fillText('新功能+', 630, 220);

                // 添加闪烁效果
                gsap.from('#detailed-canvas', {
                    opacity: 0,
                    duration: 1,
                    ease: "power2.out"
                });
            }, 500);
        }

        function showSolutionProcess() {
            currentStep = 5;
            updateProgress();
            showSection('solution-section');
            startSolutionAnimation();

            document.getElementById('solution-section').scrollIntoView({
                behavior: 'smooth'
            });
        }

        function startSolutionAnimation() {
            const canvas = document.getElementById('solution-canvas');
            const ctx = canvas.getContext('2d');

            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // 绘制解题步骤
            const steps = [
                '1. 理解题目关键词："版本替换"和"增加新功能"',
                '2. 分析各选项特点',
                '3. 匹配关键特征',
                '4. 确定答案'
            ];

            steps.forEach((step, index) => {
                setTimeout(() => {
                    ctx.fillStyle = '#2c3e50';
                    ctx.font = '18px Microsoft YaHei';
                    ctx.textAlign = 'left';
                    ctx.fillText(step, 50, 50 + index * 40);

                    // 添加动画效果
                    gsap.from('#solution-canvas', {
                        opacity: 0.5,
                        duration: 0.5
                    });
                }, index * 1000);
            });
        }

        function showComparison() {
            currentStep = 6;
            updateProgress();
            showSection('comparison-section');
            startComparisonAnimation();

            document.getElementById('comparison-section').scrollIntoView({
                behavior: 'smooth'
            });
        }

        function startComparisonAnimation() {
            const canvas = document.getElementById('comparison-canvas');
            const ctx = canvas.getContext('2d');

            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // 绘制对比表格
            const options = [
                {name: 'A. 装配的构件', feature: '用胶水代码连接', match: false},
                {name: 'B. 可修改的构件', feature: '版本替换+增加功能', match: true},
                {name: 'C. 有限制的构件', feature: '有接口但需测试', match: false},
                {name: 'D. 适应性构件', feature: '包装后直接使用', match: false}
            ];

            options.forEach((option, index) => {
                setTimeout(() => {
                    const y = 50 + index * 80;

                    // 绘制选项框
                    ctx.fillStyle = option.match ? '#27ae60' : '#e74c3c';
                    ctx.fillRect(50, y, 600, 60);

                    // 绘制文字
                    ctx.fillStyle = 'white';
                    ctx.font = 'bold 16px Microsoft YaHei';
                    ctx.textAlign = 'left';
                    ctx.fillText(option.name, 70, y + 25);
                    ctx.fillText(option.feature, 70, y + 45);

                    // 绘制匹配标识
                    ctx.fillStyle = 'white';
                    ctx.font = '20px Arial';
                    ctx.textAlign = 'right';
                    ctx.fillText(option.match ? '✓' : '✗', 630, y + 35);
                }, index * 500);
            });
        }

        function startInteractiveQuiz() {
            // 创建交互式小测验
            const quizContainer = document.getElementById('quiz-container');
            quizContainer.innerHTML = `
                <h3>🎯 互动测验：拖拽匹配</h3>
                <p>将构件特征拖拽到对应的构件类型上：</p>
                <div class="drag-area">
                    <div class="drag-items">
                        <div class="drag-item" draggable="true" data-answer="modifiable">版本替换+增加功能</div>
                        <div class="drag-item" draggable="true" data-answer="adaptive">包装后直接使用</div>
                        <div class="drag-item" draggable="true" data-answer="assembled">胶水代码连接</div>
                        <div class="drag-item" draggable="true" data-answer="restricted">有接口需测试</div>
                    </div>
                    <div class="drop-zones">
                        <div class="drop-zone" data-type="modifiable">可修改的构件</div>
                        <div class="drop-zone" data-type="adaptive">适应性构件</div>
                        <div class="drop-zone" data-type="assembled">装配的构件</div>
                        <div class="drop-zone" data-type="restricted">有限制的构件</div>
                    </div>
                </div>
            `;

            setupDragAndDrop();
        }

        function setupDragAndDrop() {
            const dragItems = document.querySelectorAll('.drag-item');
            const dropZones = document.querySelectorAll('.drop-zone');

            dragItems.forEach(item => {
                item.addEventListener('dragstart', function(e) {
                    e.dataTransfer.setData('text/plain', this.dataset.answer);
                    this.style.opacity = '0.5';
                });

                item.addEventListener('dragend', function() {
                    this.style.opacity = '1';
                });
            });

            dropZones.forEach(zone => {
                zone.addEventListener('dragover', function(e) {
                    e.preventDefault();
                    this.style.backgroundColor = '#f0f8ff';
                });

                zone.addEventListener('dragleave', function() {
                    this.style.backgroundColor = '';
                });

                zone.addEventListener('drop', function(e) {
                    e.preventDefault();
                    const draggedAnswer = e.dataTransfer.getData('text/plain');

                    if (draggedAnswer === this.dataset.type) {
                        this.style.backgroundColor = '#d4edda';
                        this.innerHTML += ' ✓';

                        // 成功动画
                        gsap.to(this, {
                            scale: 1.1,
                            duration: 0.3,
                            yoyo: true,
                            repeat: 1
                        });
                    } else {
                        this.style.backgroundColor = '#f8d7da';

                        // 错误动画
                        gsap.to(this, {
                            x: -10,
                            duration: 0.1,
                            yoyo: true,
                            repeat: 5
                        });
                    }
                });
            });
        }

        function showFinalSummary() {
            showSection('summary-section');

            // 庆祝动画
            gsap.to('.section', {
                scale: 1.02,
                duration: 0.5,
                yoyo: true,
                repeat: 1,
                ease: "power2.inOut"
            });

            // 更新进度为100%
            document.getElementById('progress-fill').style.width = '100%';
            document.getElementById('step-indicator').textContent = '学习完成！🎉';

            document.getElementById('summary-section').scrollIntoView({
                behavior: 'smooth'
            });

            // 添加彩带效果
            setTimeout(() => {
                createConfetti();
            }, 1000);
        }

        function createConfetti() {
            // 简单的彩带效果
            for (let i = 0; i < 50; i++) {
                const confetti = document.createElement('div');
                confetti.style.position = 'fixed';
                confetti.style.left = Math.random() * 100 + 'vw';
                confetti.style.top = '-10px';
                confetti.style.width = '10px';
                confetti.style.height = '10px';
                confetti.style.backgroundColor = ['#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', '#feca57'][Math.floor(Math.random() * 5)];
                confetti.style.pointerEvents = 'none';
                confetti.style.zIndex = '9999';
                document.body.appendChild(confetti);

                gsap.to(confetti, {
                    y: window.innerHeight + 100,
                    rotation: 360,
                    duration: 3 + Math.random() * 2,
                    ease: "power2.out",
                    onComplete: () => {
                        confetti.remove();
                    }
                });
            }
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            updateProgress();
            showSection('question-section');
        });
    </script>
</body>
</html>
