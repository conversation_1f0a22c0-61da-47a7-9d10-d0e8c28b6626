<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>财务系统网络架构学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            color: white;
            margin-bottom: 40px;
            animation: fadeInDown 1s ease-out;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .section {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            opacity: 0;
            transform: translateY(50px);
            animation: slideUp 0.8s ease-out forwards;
        }

        .section:nth-child(2) { animation-delay: 0.2s; }
        .section:nth-child(3) { animation-delay: 0.4s; }
        .section:nth-child(4) { animation-delay: 0.6s; }

        .section-title {
            font-size: 1.8em;
            color: #333;
            margin-bottom: 20px;
            text-align: center;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 2px;
        }

        .canvas-container {
            display: flex;
            justify-content: center;
            margin: 20px 0;
        }

        canvas {
            border: 2px solid #e0e0e0;
            border-radius: 15px;
            background: white;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .controls {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin: 20px 0;
            flex-wrap: wrap;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 25px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
        }

        .btn:active {
            transform: translateY(0);
        }

        .explanation {
            background: #f8f9ff;
            border-left: 4px solid #667eea;
            padding: 20px;
            margin: 20px 0;
            border-radius: 0 10px 10px 0;
            font-size: 16px;
            line-height: 1.6;
        }

        .quiz-section {
            background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
            color: white;
        }

        .quiz-section .section-title {
            color: white;
        }

        .option {
            background: rgba(255, 255, 255, 0.2);
            border: 2px solid transparent;
            border-radius: 15px;
            padding: 15px;
            margin: 10px 0;
            cursor: pointer;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .option:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateX(10px);
        }

        .option.selected {
            border-color: white;
            background: rgba(255, 255, 255, 0.4);
        }

        .option.correct {
            border-color: #4CAF50;
            background: rgba(76, 175, 80, 0.3);
        }

        .option.wrong {
            border-color: #f44336;
            background: rgba(244, 67, 54, 0.3);
        }

        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes slideUp {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .highlight {
            animation: pulse 2s infinite;
        }

        .progress-bar {
            width: 100%;
            height: 6px;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 3px;
            margin: 20px 0;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #4CAF50, #8BC34A);
            width: 0%;
            transition: width 0.5s ease;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏛️ 财务系统网络架构学习</h1>
            <p>通过动画和交互，轻松掌握网络安全知识</p>
        </div>

        <div class="section">
            <h2 class="section-title">📚 基础知识讲解</h2>
            <div class="canvas-container">
                <canvas id="conceptCanvas" width="800" height="400"></canvas>
            </div>
            <div class="controls">
                <button class="btn" onclick="showConcept('firewall')">🔥 防火墙</button>
                <button class="btn" onclick="showConcept('server')">🖥️ 服务器</button>
                <button class="btn" onclick="showConcept('virtualization')">☁️ 虚拟化</button>
                <button class="btn" onclick="showConcept('backup')">💾 数据备份</button>
            </div>
            <div class="explanation" id="conceptExplanation">
                点击上方按钮，学习网络架构的基础概念！
            </div>
        </div>

        <div class="section">
            <h2 class="section-title">🏗️ 方案对比演示</h2>
            <div class="canvas-container">
                <canvas id="comparisonCanvas" width="800" height="500"></canvas>
            </div>
            <div class="controls">
                <button class="btn" onclick="showScheme(1)">方案一</button>
                <button class="btn" onclick="showScheme(2)">方案二</button>
                <button class="btn" onclick="compareSchemes()">对比分析</button>
            </div>
            <div class="explanation" id="schemeExplanation">
                选择方案查看详细架构图和特点分析
            </div>
        </div>

        <div class="section quiz-section">
            <h2 class="section-title">🎯 互动答题</h2>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
            <div id="questionContainer">
                <h3 style="margin-bottom: 20px;">与方案一相比，方案二还有一些缺点，下列不属于其缺点的是？</h3>
                <div class="option" onclick="selectOption(this, 'A')">
                    A. 缺少企业级磁盘阵列，不能将数据进行统一的存储与管理
                </div>
                <div class="option" onclick="selectOption(this, 'B')">
                    B. 缺少网闸，不能实现财务系统与Internet的物理隔离
                </div>
                <div class="option" onclick="selectOption(this, 'C')">
                    C. 缺少安全审计，不便于相关行为的记录、存储与分析
                </div>
                <div class="option" onclick="selectOption(this, 'D')">
                    D. 缺少内部财务用户接口，不便于快速管理与维护
                </div>
            </div>
            <div class="controls">
                <button class="btn" onclick="checkAnswer()" id="checkBtn" style="display:none;">检查答案</button>
                <button class="btn" onclick="showExplanation()" id="explainBtn" style="display:none;">查看解析</button>
            </div>
            <div class="explanation" id="answerExplanation" style="display:none;">
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let currentConcept = '';
        let currentScheme = 0;
        let selectedOption = '';
        let animationId;

        // Canvas 上下文
        const conceptCanvas = document.getElementById('conceptCanvas');
        const conceptCtx = conceptCanvas.getContext('2d');
        const comparisonCanvas = document.getElementById('comparisonCanvas');
        const comparisonCtx = comparisonCanvas.getContext('2d');

        // 动画时间
        let animationTime = 0;

        // 基础概念展示
        function showConcept(concept) {
            currentConcept = concept;
            const explanations = {
                firewall: {
                    title: '🔥 防火墙技术',
                    content: '防火墙是网络安全的第一道防线，就像城堡的护城河。它检查所有进出网络的数据包，根据预设规则决定是否允许通过。在财务系统中，防火墙负责地址映射，让校园网用户能够安全访问内部服务器。'
                },
                server: {
                    title: '🖥️ 服务器架构',
                    content: '服务器是提供服务的计算机。方案一使用4台物理服务器：3台财务应用服务器处理业务，1台备份服务器保护数据。这种架构简单直接，但资源利用率可能不高。'
                },
                virtualization: {
                    title: '☁️ 虚拟化技术',
                    content: '虚拟化技术可以在一台物理服务器上运行多个虚拟机，就像一栋大楼里有多个房间。方案二用2台物理服务器创建多个虚拟机，提高资源利用率，还能在故障时快速迁移业务。'
                },
                backup: {
                    title: '💾 数据备份',
                    content: '数据备份是数据安全的重要保障，就像为重要文件制作副本。方案一有专门的备份服务器定期备份数据，而方案二在虚拟化环境中也需要考虑数据备份策略。'
                }
            };

            document.getElementById('conceptExplanation').innerHTML = 
                `<h3>${explanations[concept].title}</h3><p>${explanations[concept].content}</p>`;
            
            animateConceptCanvas(concept);
        }

        // 概念动画
        function animateConceptCanvas(concept) {
            conceptCtx.clearRect(0, 0, conceptCanvas.width, conceptCanvas.height);

            if (animationId) {
                cancelAnimationFrame(animationId);
            }

            animationTime = 0;

            function animate() {
                conceptCtx.clearRect(0, 0, conceptCanvas.width, conceptCanvas.height);

                switch(concept) {
                    case 'firewall':
                        drawFirewall();
                        break;
                    case 'server':
                        drawServer();
                        break;
                    case 'virtualization':
                        drawVirtualization();
                        break;
                    case 'backup':
                        drawBackup();
                        break;
                }

                animationTime += 0.02;
                animationId = requestAnimationFrame(animate);
            }

            animate();
        }

        // 绘制防火墙动画
        function drawFirewall() {
            const centerX = conceptCanvas.width / 2;
            const centerY = conceptCanvas.height / 2;

            // 绘制网络边界
            conceptCtx.strokeStyle = '#667eea';
            conceptCtx.lineWidth = 3;
            conceptCtx.setLineDash([]);
            conceptCtx.strokeRect(50, 50, conceptCanvas.width - 100, conceptCanvas.height - 100);

            // 绘制防火墙
            const firewallX = centerX;
            const firewallY = centerY;
            const pulse = Math.sin(animationTime * 5) * 0.1 + 1;

            conceptCtx.fillStyle = '#ff6b6b';
            conceptCtx.beginPath();
            conceptCtx.roundRect(firewallX - 40 * pulse, firewallY - 60 * pulse, 80 * pulse, 120 * pulse, 10);
            conceptCtx.fill();

            // 防火墙图标
            conceptCtx.fillStyle = 'white';
            conceptCtx.font = '30px Arial';
            conceptCtx.textAlign = 'center';
            conceptCtx.fillText('🔥', firewallX, firewallY + 10);

            // 数据包动画
            const packets = [
                {x: 100, y: centerY - 30, color: '#4CAF50', status: 'allowed'},
                {x: 100, y: centerY, color: '#f44336', status: 'blocked'},
                {x: 100, y: centerY + 30, color: '#4CAF50', status: 'allowed'}
            ];

            packets.forEach((packet, index) => {
                const progress = (animationTime * 100 + index * 50) % 600;
                let x = 100 + progress;

                if (packet.status === 'blocked' && x > firewallX - 50) {
                    x = firewallX - 50;
                    // 绘制阻止效果
                    conceptCtx.fillStyle = '#f44336';
                    conceptCtx.font = '20px Arial';
                    conceptCtx.fillText('❌', x + 20, packet.y + 5);
                } else if (packet.status === 'allowed' && x > firewallX + 50) {
                    // 通过防火墙后继续移动
                }

                conceptCtx.fillStyle = packet.color;
                conceptCtx.beginPath();
                conceptCtx.arc(x, packet.y, 8, 0, Math.PI * 2);
                conceptCtx.fill();
            });

            // 标签
            conceptCtx.fillStyle = '#333';
            conceptCtx.font = '16px Arial';
            conceptCtx.textAlign = 'left';
            conceptCtx.fillText('校园网', 60, 40);
            conceptCtx.textAlign = 'right';
            conceptCtx.fillText('内部服务器', conceptCanvas.width - 60, 40);
        }

        // 绘制服务器动画
        function drawServer() {
            const servers = [
                {x: 150, y: 150, label: '财务服务器1', active: true},
                {x: 350, y: 150, label: '财务服务器2', active: true},
                {x: 550, y: 150, label: '财务服务器3', active: true},
                {x: 350, y: 300, label: '备份服务器', active: true}
            ];

            servers.forEach((server, index) => {
                const pulse = Math.sin(animationTime * 3 + index) * 0.05 + 1;
                const serverSize = 60 * pulse;

                // 服务器主体
                conceptCtx.fillStyle = server.active ? '#4CAF50' : '#ccc';
                conceptCtx.beginPath();
                conceptCtx.roundRect(server.x - serverSize/2, server.y - serverSize/2, serverSize, serverSize, 8);
                conceptCtx.fill();

                // 服务器图标
                conceptCtx.fillStyle = 'white';
                conceptCtx.font = '24px Arial';
                conceptCtx.textAlign = 'center';
                conceptCtx.fillText('🖥️', server.x, server.y + 8);

                // 标签
                conceptCtx.fillStyle = '#333';
                conceptCtx.font = '12px Arial';
                conceptCtx.fillText(server.label, server.x, server.y + 50);

                // 数据流动画
                if (index < 3) { // 财务服务器到备份服务器
                    const dataX = server.x + Math.sin(animationTime * 2 + index) * 20;
                    const dataY = server.y + 30 + (servers[3].y - server.y - 30) * ((Math.sin(animationTime * 2 + index) + 1) / 2);

                    conceptCtx.fillStyle = '#2196F3';
                    conceptCtx.beginPath();
                    conceptCtx.arc(dataX, dataY, 4, 0, Math.PI * 2);
                    conceptCtx.fill();
                }
            });

            // 连接线
            conceptCtx.strokeStyle = '#ddd';
            conceptCtx.lineWidth = 2;
            conceptCtx.setLineDash([5, 5]);
            servers.slice(0, 3).forEach(server => {
                conceptCtx.beginPath();
                conceptCtx.moveTo(server.x, server.y + 30);
                conceptCtx.lineTo(servers[3].x, servers[3].y - 30);
                conceptCtx.stroke();
            });
        }

        // 绘制虚拟化动画
        function drawVirtualization() {
            const physicalServers = [
                {x: 200, y: 200, vms: 3},
                {x: 600, y: 200, vms: 3}
            ];

            physicalServers.forEach((server, serverIndex) => {
                // 物理服务器
                const pulse = Math.sin(animationTime * 2 + serverIndex) * 0.05 + 1;
                conceptCtx.fillStyle = '#2196F3';
                conceptCtx.beginPath();
                conceptCtx.roundRect(server.x - 80, server.y - 60, 160, 120, 10);
                conceptCtx.fill();

                // 虚拟机
                for (let i = 0; i < server.vms; i++) {
                    const vmX = server.x - 50 + (i * 35);
                    const vmY = server.y - 20 + Math.sin(animationTime * 3 + i + serverIndex * 3) * 5;
                    const vmPulse = Math.sin(animationTime * 4 + i + serverIndex * 3) * 0.1 + 1;

                    conceptCtx.fillStyle = `hsl(${120 + i * 60}, 70%, 60%)`;
                    conceptCtx.beginPath();
                    conceptCtx.roundRect(vmX - 15 * vmPulse, vmY - 15 * vmPulse, 30 * vmPulse, 30 * vmPulse, 5);
                    conceptCtx.fill();

                    conceptCtx.fillStyle = 'white';
                    conceptCtx.font = '12px Arial';
                    conceptCtx.textAlign = 'center';
                    conceptCtx.fillText('VM', vmX, vmY + 4);
                }

                // 服务器标签
                conceptCtx.fillStyle = 'white';
                conceptCtx.font = '16px Arial';
                conceptCtx.textAlign = 'center';
                conceptCtx.fillText(`物理服务器${serverIndex + 1}`, server.x, server.y + 80);
            });

            // 故障转移动画
            if (Math.sin(animationTime) > 0.5) {
                // 模拟服务器1故障
                conceptCtx.fillStyle = 'rgba(244, 67, 54, 0.7)';
                conceptCtx.beginPath();
                conceptCtx.roundRect(120, 140, 160, 120, 10);
                conceptCtx.fill();

                conceptCtx.fillStyle = '#f44336';
                conceptCtx.font = '20px Arial';
                conceptCtx.textAlign = 'center';
                conceptCtx.fillText('❌ 故障', 200, 200);

                // 迁移箭头
                conceptCtx.strokeStyle = '#ff9800';
                conceptCtx.lineWidth = 4;
                conceptCtx.setLineDash([]);
                conceptCtx.beginPath();
                conceptCtx.moveTo(280, 200);
                conceptCtx.lineTo(520, 200);
                conceptCtx.stroke();

                // 箭头头部
                conceptCtx.fillStyle = '#ff9800';
                conceptCtx.beginPath();
                conceptCtx.moveTo(520, 200);
                conceptCtx.lineTo(510, 190);
                conceptCtx.lineTo(510, 210);
                conceptCtx.closePath();
                conceptCtx.fill();

                conceptCtx.fillStyle = '#ff9800';
                conceptCtx.font = '14px Arial';
                conceptCtx.fillText('业务迁移', 400, 180);
            }
        }

        // 绘制备份动画
        function drawBackup() {
            const mainServers = [
                {x: 150, y: 150},
                {x: 250, y: 150},
                {x: 350, y: 150}
            ];
            const backupServer = {x: 550, y: 250};

            // 主服务器
            mainServers.forEach((server, index) => {
                const pulse = Math.sin(animationTime * 2 + index) * 0.05 + 1;
                conceptCtx.fillStyle = '#4CAF50';
                conceptCtx.beginPath();
                conceptCtx.roundRect(server.x - 30, server.y - 30, 60, 60, 8);
                conceptCtx.fill();

                conceptCtx.fillStyle = 'white';
                conceptCtx.font = '20px Arial';
                conceptCtx.textAlign = 'center';
                conceptCtx.fillText('💾', server.x, server.y + 8);
            });

            // 备份服务器
            const backupPulse = Math.sin(animationTime * 3) * 0.1 + 1;
            conceptCtx.fillStyle = '#2196F3';
            conceptCtx.beginPath();
            conceptCtx.roundRect(backupServer.x - 40 * backupPulse, backupServer.y - 40 * backupPulse,
                               80 * backupPulse, 80 * backupPulse, 10);
            conceptCtx.fill();

            conceptCtx.fillStyle = 'white';
            conceptCtx.font = '24px Arial';
            conceptCtx.textAlign = 'center';
            conceptCtx.fillText('🗄️', backupServer.x, backupServer.y + 8);

            // 备份数据流
            mainServers.forEach((server, index) => {
                const progress = (animationTime * 100 + index * 100) % 400;
                const startX = server.x;
                const startY = server.y + 30;
                const endX = backupServer.x;
                const endY = backupServer.y - 40;

                const dataX = startX + (endX - startX) * (progress / 400);
                const dataY = startY + (endY - startY) * (progress / 400);

                if (progress < 400) {
                    conceptCtx.fillStyle = '#ff9800';
                    conceptCtx.beginPath();
                    conceptCtx.arc(dataX, dataY, 6, 0, Math.PI * 2);
                    conceptCtx.fill();

                    // 数据包标识
                    conceptCtx.fillStyle = 'white';
                    conceptCtx.font = '10px Arial';
                    conceptCtx.textAlign = 'center';
                    conceptCtx.fillText('📄', dataX, dataY + 3);
                }

                // 连接线
                conceptCtx.strokeStyle = '#ddd';
                conceptCtx.lineWidth = 2;
                conceptCtx.setLineDash([5, 5]);
                conceptCtx.beginPath();
                conceptCtx.moveTo(startX, startY);
                conceptCtx.lineTo(endX, endY);
                conceptCtx.stroke();
            });

            // 备份进度
            conceptCtx.fillStyle = '#333';
            conceptCtx.font = '14px Arial';
            conceptCtx.textAlign = 'center';
            conceptCtx.fillText('定期备份中...', backupServer.x, backupServer.y + 60);
        }

        // 方案展示
        function showScheme(schemeNumber) {
            currentScheme = schemeNumber;
            const explanations = {
                1: {
                    title: '方案一：传统物理架构',
                    content: '采用4台物理服务器：3台财务应用服务器 + 1台备份服务器。优点是架构简单、稳定可靠；缺点是资源利用率不高，扩展性有限。'
                },
                2: {
                    title: '方案二：虚拟化架构',
                    content: '采用2台物理服务器 + 虚拟化技术。优点是资源利用率高、支持故障转移、扩展灵活；缺点是技术复杂度较高。'
                }
            };

            document.getElementById('schemeExplanation').innerHTML =
                `<h3>${explanations[schemeNumber].title}</h3><p>${explanations[schemeNumber].content}</p>`;

            animateSchemeCanvas(schemeNumber);
        }

        // 方案对比
        function compareSchemes() {
            document.getElementById('schemeExplanation').innerHTML =
                `<h3>🔍 方案对比分析</h3>
                <p><strong>相同点：</strong>都使用防火墙进行安全防护和地址映射</p>
                <p><strong>方案一优势：</strong>架构简单，维护容易，性能稳定</p>
                <p><strong>方案二优势：</strong>资源利用率高，支持故障自动转移，扩展性好</p>
                <p><strong>方案二缺点：</strong>缺少统一存储、安全审计、用户接口等功能</p>`;

            animateComparisonCanvas();
        }

        // 方案动画
        function animateSchemeCanvas(scheme) {
            if (animationId) {
                cancelAnimationFrame(animationId);
            }

            animationTime = 0;

            function animate() {
                comparisonCtx.clearRect(0, 0, comparisonCanvas.width, comparisonCanvas.height);

                if (scheme === 1) {
                    drawScheme1();
                } else if (scheme === 2) {
                    drawScheme2();
                }

                animationTime += 0.02;
                animationId = requestAnimationFrame(animate);
            }

            animate();
        }

        // 绘制方案一
        function drawScheme1() {
            // 校园网
            comparisonCtx.fillStyle = '#e3f2fd';
            comparisonCtx.beginPath();
            comparisonCtx.roundRect(50, 50, 150, 100, 10);
            comparisonCtx.fill();
            comparisonCtx.fillStyle = '#1976d2';
            comparisonCtx.font = '16px Arial';
            comparisonCtx.textAlign = 'center';
            comparisonCtx.fillText('校园网', 125, 105);

            // 防火墙
            const firewallPulse = Math.sin(animationTime * 3) * 0.1 + 1;
            comparisonCtx.fillStyle = '#ff5722';
            comparisonCtx.beginPath();
            comparisonCtx.roundRect(280 - 30 * firewallPulse, 80 - 20 * firewallPulse,
                                   60 * firewallPulse, 40 * firewallPulse, 5);
            comparisonCtx.fill();
            comparisonCtx.fillStyle = 'white';
            comparisonCtx.font = '20px Arial';
            comparisonCtx.textAlign = 'center';
            comparisonCtx.fillText('🔥', 280, 105);

            // 财务服务器
            const servers = [
                {x: 500, y: 100, label: '财务1'},
                {x: 600, y: 100, label: '财务2'},
                {x: 700, y: 100, label: '财务3'}
            ];

            servers.forEach((server, index) => {
                const pulse = Math.sin(animationTime * 2 + index) * 0.05 + 1;
                comparisonCtx.fillStyle = '#4caf50';
                comparisonCtx.beginPath();
                comparisonCtx.roundRect(server.x - 25, server.y - 25, 50, 50, 8);
                comparisonCtx.fill();
                comparisonCtx.fillStyle = 'white';
                comparisonCtx.font = '16px Arial';
                comparisonCtx.textAlign = 'center';
                comparisonCtx.fillText('💻', server.x, server.y + 5);
                comparisonCtx.fillStyle = '#333';
                comparisonCtx.font = '12px Arial';
                comparisonCtx.fillText(server.label, server.x, server.y + 40);
            });

            // 备份服务器
            const backupPulse = Math.sin(animationTime * 2.5) * 0.1 + 1;
            comparisonCtx.fillStyle = '#2196f3';
            comparisonCtx.beginPath();
            comparisonCtx.roundRect(600 - 30 * backupPulse, 250 - 30 * backupPulse,
                                   60 * backupPulse, 60 * backupPulse, 10);
            comparisonCtx.fill();
            comparisonCtx.fillStyle = 'white';
            comparisonCtx.font = '20px Arial';
            comparisonCtx.textAlign = 'center';
            comparisonCtx.fillText('🗄️', 600, 255);
            comparisonCtx.fillStyle = '#333';
            comparisonCtx.font = '12px Arial';
            comparisonCtx.fillText('备份服务器', 600, 290);

            // 连接线
            comparisonCtx.strokeStyle = '#666';
            comparisonCtx.lineWidth = 2;
            comparisonCtx.setLineDash([]);

            // 校园网到防火墙
            comparisonCtx.beginPath();
            comparisonCtx.moveTo(200, 100);
            comparisonCtx.lineTo(250, 100);
            comparisonCtx.stroke();

            // 防火墙到服务器
            comparisonCtx.beginPath();
            comparisonCtx.moveTo(340, 100);
            comparisonCtx.lineTo(450, 100);
            comparisonCtx.stroke();

            // 服务器间连接
            servers.forEach(server => {
                comparisonCtx.beginPath();
                comparisonCtx.moveTo(server.x, server.y + 25);
                comparisonCtx.lineTo(600, 220);
                comparisonCtx.stroke();
            });
        }

        // 绘制方案二
        function drawScheme2() {
            // 校园网
            comparisonCtx.fillStyle = '#e3f2fd';
            comparisonCtx.beginPath();
            comparisonCtx.roundRect(50, 50, 150, 100, 10);
            comparisonCtx.fill();
            comparisonCtx.fillStyle = '#1976d2';
            comparisonCtx.font = '16px Arial';
            comparisonCtx.textAlign = 'center';
            comparisonCtx.fillText('校园网', 125, 105);

            // 防火墙
            const firewallPulse = Math.sin(animationTime * 3) * 0.1 + 1;
            comparisonCtx.fillStyle = '#ff5722';
            comparisonCtx.beginPath();
            comparisonCtx.roundRect(280 - 30 * firewallPulse, 80 - 20 * firewallPulse,
                                   60 * firewallPulse, 40 * firewallPulse, 5);
            comparisonCtx.fill();
            comparisonCtx.fillStyle = 'white';
            comparisonCtx.font = '20px Arial';
            comparisonCtx.textAlign = 'center';
            comparisonCtx.fillText('🔥', 280, 105);

            // 物理服务器
            const servers = [
                {x: 500, y: 150, label: '物理服务器1'},
                {x: 700, y: 150, label: '物理服务器2'}
            ];

            servers.forEach((server, index) => {
                const pulse = Math.sin(animationTime * 2 + index) * 0.05 + 1;
                comparisonCtx.fillStyle = '#2196f3';
                comparisonCtx.beginPath();
                comparisonCtx.roundRect(server.x - 60, server.y - 40, 120, 80, 10);
                comparisonCtx.fill();
                comparisonCtx.fillStyle = 'white';
                comparisonCtx.font = '16px Arial';
                comparisonCtx.textAlign = 'center';
                comparisonCtx.fillText('🖥️', server.x, server.y + 5);
                comparisonCtx.fillStyle = '#333';
                comparisonCtx.font = '12px Arial';
                comparisonCtx.fillText(server.label, server.x, server.y + 60);

                // 虚拟机
                for (let i = 0; i < 3; i++) {
                    const vmX = server.x - 30 + i * 30;
                    const vmY = server.y - 10;
                    const vmPulse = Math.sin(animationTime * 4 + i + index * 3) * 0.1 + 1;

                    comparisonCtx.fillStyle = `hsl(${120 + i * 60}, 70%, 60%)`;
                    comparisonCtx.beginPath();
                    comparisonCtx.roundRect(vmX - 10 * vmPulse, vmY - 10 * vmPulse,
                                           20 * vmPulse, 20 * vmPulse, 5);
                    comparisonCtx.fill();

                    comparisonCtx.fillStyle = 'white';
                    comparisonCtx.font = '10px Arial';
                    comparisonCtx.textAlign = 'center';
                    comparisonCtx.fillText('VM', vmX, vmY + 3);
                }
            });

            // 故障转移
            if (Math.sin(animationTime) > 0.5) {
                // 故障标识
                comparisonCtx.fillStyle = 'rgba(244, 67, 54, 0.3)';
                comparisonCtx.beginPath();
                comparisonCtx.roundRect(440, 110, 120, 80, 10);
                comparisonCtx.fill();

                comparisonCtx.fillStyle = '#f44336';
                comparisonCtx.font = '16px Arial';
                comparisonCtx.textAlign = 'center';
                comparisonCtx.fillText('❌', 500, 150);

                // 迁移箭头
                comparisonCtx.strokeStyle = '#ff9800';
                comparisonCtx.lineWidth = 3;
                comparisonCtx.setLineDash([5, 5]);
                comparisonCtx.beginPath();
                comparisonCtx.moveTo(560, 150);
                comparisonCtx.lineTo(640, 150);
                comparisonCtx.stroke();

                // 箭头头部
                comparisonCtx.fillStyle = '#ff9800';
                comparisonCtx.beginPath();
                comparisonCtx.moveTo(640, 150);
                comparisonCtx.lineTo(630, 145);
                comparisonCtx.lineTo(630, 155);
                comparisonCtx.closePath();
                comparisonCtx.fill();

                comparisonCtx.fillStyle = '#ff9800';
                comparisonCtx.font = '12px Arial';
                comparisonCtx.fillText('业务迁移', 600, 135);
            }

            // 连接线
            comparisonCtx.strokeStyle = '#666';
            comparisonCtx.lineWidth = 2;
            comparisonCtx.setLineDash([]);

            // 校园网到防火墙
            comparisonCtx.beginPath();
            comparisonCtx.moveTo(200, 100);
            comparisonCtx.lineTo(250, 100);
            comparisonCtx.stroke();

            // 防火墙到服务器
            comparisonCtx.beginPath();
            comparisonCtx.moveTo(340, 100);
            comparisonCtx.lineTo(400, 100);
            comparisonCtx.lineTo(400, 150);
            comparisonCtx.lineTo(440, 150);
            comparisonCtx.stroke();

            // 服务器间连接
            comparisonCtx.beginPath();
            comparisonCtx.moveTo(560, 150);
            comparisonCtx.lineTo(640, 150);
            comparisonCtx.stroke();

            // 缺点标注
            const disadvantages = [
                {x: 600, y: 250, text: '缺少企业级磁盘阵列', icon: '💾'},
                {x: 600, y: 300, text: '缺少安全审计', icon: '🔒'},
                {x: 600, y: 350, text: '缺少内部用户接口', icon: '👤'}
            ];

            disadvantages.forEach((item, index) => {
                comparisonCtx.fillStyle = '#f44336';
                comparisonCtx.font = '14px Arial';
                comparisonCtx.textAlign = 'center';
                comparisonCtx.fillText(item.icon, item.x - 100, item.y);

                comparisonCtx.fillStyle = '#333';
                comparisonCtx.textAlign = 'left';
                comparisonCtx.fillText(item.text, item.x - 80, item.y);

                comparisonCtx.strokeStyle = '#f44336';
                comparisonCtx.lineWidth = 1;
                comparisonCtx.setLineDash([3, 3]);
                comparisonCtx.beginPath();
                comparisonCtx.moveTo(item.x - 110, item.y - 5);
                comparisonCtx.lineTo(item.x + 100, item.y - 5);
                comparisonCtx.stroke();
            });
        }

        // 方案对比动画
        function animateComparisonCanvas() {
            if (animationId) {
                cancelAnimationFrame(animationId);
            }

            animationTime = 0;

            function animate() {
                comparisonCtx.clearRect(0, 0, comparisonCanvas.width, comparisonCanvas.height);

                // 分割线
                comparisonCtx.strokeStyle = '#ddd';
                comparisonCtx.lineWidth = 2;
                comparisonCtx.setLineDash([5, 5]);
                comparisonCtx.beginPath();
                comparisonCtx.moveTo(comparisonCanvas.width / 2, 50);
                comparisonCtx.lineTo(comparisonCanvas.width / 2, comparisonCanvas.height - 50);
                comparisonCtx.stroke();

                // 标题
                comparisonCtx.fillStyle = '#333';
                comparisonCtx.font = '18px Arial';
                comparisonCtx.textAlign = 'center';
                comparisonCtx.fillText('方案一：传统架构', comparisonCanvas.width / 4, 30);
                comparisonCtx.fillText('方案二：虚拟化架构', comparisonCanvas.width * 3 / 4, 30);

                // 简化版方案一
                drawSimplifiedScheme1(comparisonCanvas.width / 4, comparisonCanvas.height / 2 - 50);

                // 简化版方案二
                drawSimplifiedScheme2(comparisonCanvas.width * 3 / 4, comparisonCanvas.height / 2 - 50);

                // 优缺点比较
                drawComparison();

                animationTime += 0.02;
                animationId = requestAnimationFrame(animate);
            }

            animate();
        }

        // 简化方案一绘制
        function drawSimplifiedScheme1(centerX, centerY) {
            // 4台服务器
            const servers = [
                {x: centerX - 60, y: centerY - 30, color: '#4caf50'},
                {x: centerX - 20, y: centerY - 30, color: '#4caf50'},
                {x: centerX + 20, y: centerY - 30, color: '#4caf50'},
                {x: centerX, y: centerY + 30, color: '#2196f3'}
            ];

            servers.forEach((server, index) => {
                const pulse = Math.sin(animationTime * 2 + index) * 0.05 + 1;
                comparisonCtx.fillStyle = server.color;
                comparisonCtx.beginPath();
                comparisonCtx.roundRect(server.x - 15 * pulse, server.y - 15 * pulse,
                                       30 * pulse, 30 * pulse, 5);
                comparisonCtx.fill();

                comparisonCtx.fillStyle = 'white';
                comparisonCtx.font = '12px Arial';
                comparisonCtx.textAlign = 'center';
                comparisonCtx.fillText(index < 3 ? '💻' : '🗄️', server.x, server.y + 4);
            });

            // 标签
            comparisonCtx.fillStyle = '#333';
            comparisonCtx.font = '12px Arial';
            comparisonCtx.textAlign = 'center';
            comparisonCtx.fillText('3台财务服务器', centerX - 20, centerY - 60);
            comparisonCtx.fillText('1台备份服务器', centerX, centerY + 60);
        }

        // 简化方案二绘制
        function drawSimplifiedScheme2(centerX, centerY) {
            // 2台物理服务器
            const servers = [
                {x: centerX - 40, y: centerY},
                {x: centerX + 40, y: centerY}
            ];

            servers.forEach((server, index) => {
                const pulse = Math.sin(animationTime * 2 + index) * 0.05 + 1;
                comparisonCtx.fillStyle = '#2196f3';
                comparisonCtx.beginPath();
                comparisonCtx.roundRect(server.x - 25 * pulse, server.y - 25 * pulse,
                                       50 * pulse, 50 * pulse, 8);
                comparisonCtx.fill();

                // 虚拟机
                for (let i = 0; i < 3; i++) {
                    const vmX = server.x - 15 + i * 15;
                    const vmY = server.y - 5;
                    const vmPulse = Math.sin(animationTime * 4 + i + index * 3) * 0.1 + 1;

                    comparisonCtx.fillStyle = `hsl(${120 + i * 60}, 70%, 60%)`;
                    comparisonCtx.beginPath();
                    comparisonCtx.roundRect(vmX - 5 * vmPulse, vmY - 5 * vmPulse,
                                           10 * vmPulse, 10 * vmPulse, 2);
                    comparisonCtx.fill();
                }
            });

            // 标签
            comparisonCtx.fillStyle = '#333';
            comparisonCtx.font = '12px Arial';
            comparisonCtx.textAlign = 'center';
            comparisonCtx.fillText('2台物理服务器', centerX, centerY - 50);
            comparisonCtx.fillText('多个虚拟机', centerX, centerY + 40);
        }

        // 绘制比较信息
        function drawComparison() {
            const comparisons = [
                {y: 350, text: '资源利用率', scheme1: '较低', scheme2: '较高', color1: '#f44336', color2: '#4caf50'},
                {y: 380, text: '故障转移', scheme1: '手动', scheme2: '自动', color1: '#ff9800', color2: '#4caf50'},
                {y: 410, text: '扩展性', scheme1: '有限', scheme2: '灵活', color1: '#ff9800', color2: '#4caf50'},
                {y: 440, text: '维护复杂度', scheme1: '简单', scheme2: '复杂', color1: '#4caf50', color2: '#ff9800'}
            ];

            comparisons.forEach(comp => {
                comparisonCtx.fillStyle = '#333';
                comparisonCtx.font = '14px Arial';
                comparisonCtx.textAlign = 'center';
                comparisonCtx.fillText(comp.text, comparisonCanvas.width / 2, comp.y);

                comparisonCtx.fillStyle = comp.color1;
                comparisonCtx.textAlign = 'center';
                comparisonCtx.fillText(comp.scheme1, comparisonCanvas.width / 4, comp.y);

                comparisonCtx.fillStyle = comp.color2;
                comparisonCtx.fillText(comp.scheme2, comparisonCanvas.width * 3 / 4, comp.y);
            });
        }

        // 选择答案
        function selectOption(element, option) {
            // 清除之前的选择
            document.querySelectorAll('.option').forEach(opt => {
                opt.classList.remove('selected');
            });

            // 标记当前选择
            element.classList.add('selected');
            selectedOption = option;

            // 显示检查按钮
            document.getElementById('checkBtn').style.display = 'inline-block';

            // 更新进度条
            document.getElementById('progressFill').style.width = '50%';
        }

        // 检查答案
        function checkAnswer() {
            const options = document.querySelectorAll('.option');
            const correctAnswer = 'B';

            options.forEach(option => {
                const optionLetter = option.textContent.trim().charAt(0);
                if (optionLetter === correctAnswer) {
                    option.classList.add('correct');
                } else if (optionLetter === selectedOption && optionLetter !== correctAnswer) {
                    option.classList.add('wrong');
                }
            });

            // 隐藏检查按钮，显示解析按钮
            document.getElementById('checkBtn').style.display = 'none';
            document.getElementById('explainBtn').style.display = 'inline-block';

            // 更新进度条
            document.getElementById('progressFill').style.width = '75%';
        }

        // 显示解析
        function showExplanation() {
            const explanation = `
                <h3>📝 答案解析</h3>
                <p><strong>正确答案：B</strong></p>
                <p><strong>解析：</strong></p>
                <p>题目问的是"不属于方案二缺点"的选项。我们来分析每个选项：</p>
                <ul>
                    <li><strong>A选项：</strong>缺少企业级磁盘阵列 - 这确实是方案二的缺点</li>
                    <li><strong>B选项：</strong>缺少网闸 - 这不是缺点！如果加了网闸，就无法实现"用户通过校园网访问财务系统"的需求</li>
                    <li><strong>C选项：</strong>缺少安全审计 - 这确实是方案二的缺点</li>
                    <li><strong>D选项：</strong>缺少内部用户接口 - 这确实是方案二的缺点</li>
                </ul>
                <p><strong>关键理解：</strong>网闸是用于物理隔离的设备，如果使用网闸，就无法实现校园网用户直接访问财务系统的需求。</p>
            `;

            document.getElementById('answerExplanation').innerHTML = explanation;
            document.getElementById('answerExplanation').style.display = 'block';
            document.getElementById('explainBtn').style.display = 'none';

            // 完成进度条
            document.getElementById('progressFill').style.width = '100%';
        }

        // 初始化
        window.onload = function() {
            // 默认显示防火墙概念
            showConcept('firewall');
        };

        // Canvas 圆角矩形支持
        if (!CanvasRenderingContext2D.prototype.roundRect) {
            CanvasRenderingContext2D.prototype.roundRect = function(x, y, width, height, radius) {
                this.beginPath();
                this.moveTo(x + radius, y);
                this.lineTo(x + width - radius, y);
                this.quadraticCurveTo(x + width, y, x + width, y + radius);
                this.lineTo(x + width, y + height - radius);
                this.quadraticCurveTo(x + width, y + height, x + width - radius, y + height);
                this.lineTo(x + radius, y + height);
                this.quadraticCurveTo(x, y + height, x, y + height - radius);
                this.lineTo(x, y + radius);
                this.quadraticCurveTo(x, y, x + radius, y);
                this.closePath();
            };
        }
    </script>
</body>
</html>
