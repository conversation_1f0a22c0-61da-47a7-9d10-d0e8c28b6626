<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>邮件协议学习 - 零基础动画教学</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            animation: fadeInDown 1s ease-out;
        }

        .title {
            font-size: 2.5rem;
            color: white;
            margin-bottom: 20px;
            text-shadow: 0 4px 8px rgba(0,0,0,0.3);
        }

        .subtitle {
            font-size: 1.2rem;
            color: rgba(255,255,255,0.9);
            font-weight: 300;
        }

        .question-card {
            background: white;
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            animation: fadeInUp 1s ease-out 0.3s both;
        }

        .question-title {
            font-size: 1.5rem;
            color: #333;
            margin-bottom: 30px;
            line-height: 1.6;
        }

        .options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .option {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .option:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }

        .option.correct {
            background: linear-gradient(135deg, #4CAF50, #45a049);
            color: white;
            border-color: #4CAF50;
        }

        .option.wrong {
            background: linear-gradient(135deg, #f44336, #d32f2f);
            color: white;
            border-color: #f44336;
        }

        .canvas-container {
            background: white;
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            animation: fadeInUp 1s ease-out 0.6s both;
        }

        #animationCanvas {
            width: 100%;
            height: 400px;
            border-radius: 15px;
            background: linear-gradient(135deg, #e3f2fd, #bbdefb);
        }

        .explanation {
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            animation: fadeInUp 1s ease-out 0.9s both;
        }

        .explanation h3 {
            color: #333;
            margin-bottom: 20px;
            font-size: 1.4rem;
        }

        .explanation p {
            color: #666;
            line-height: 1.8;
            margin-bottom: 15px;
        }

        .protocol-box {
            display: inline-block;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            margin: 5px;
            font-weight: bold;
        }

        .start-btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1.1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 20px auto;
            display: block;
        }

        .start-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
        }

        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .pulse {
            animation: pulse 2s infinite;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">📧 邮件协议学习</h1>
            <p class="subtitle">零基础动画教学 - 让复杂概念变得简单有趣</p>
        </div>

        <div class="question-card">
            <h2 class="question-title">
                📝 题目：电子邮件客户端通过发起对（ ）服务器的（ ）端口的 TCP 连接来进行邮件发送。
            </h2>
            
            <div class="options">
                <div class="option" data-answer="A">
                    <strong>A. POP3</strong>
                    <div>邮局协议</div>
                </div>
                <div class="option" data-answer="B">
                    <strong>B. SMTP</strong>
                    <div>简单邮件传输协议</div>
                </div>
                <div class="option" data-answer="C">
                    <strong>C. HTTP</strong>
                    <div>超文本传输协议</div>
                </div>
                <div class="option" data-answer="D">
                    <strong>D. IMAP</strong>
                    <div>互联网消息访问协议</div>
                </div>
            </div>
            
            <button class="start-btn" onclick="startAnimation()">🎬 开始动画演示</button>
        </div>

        <div class="canvas-container">
            <canvas id="animationCanvas"></canvas>
        </div>

        <div class="explanation">
            <h3>🎯 知识点详解</h3>
            <p><strong>关键理解：</strong>题目问的是"邮件发送"，这是关键词！</p>
            
            <h3>📚 邮件协议家族介绍</h3>
            <p>
                <span class="protocol-box">SMTP</span> - 负责发送邮件（Simple Mail Transfer Protocol）<br>
                <span class="protocol-box">POP3</span> - 负责接收邮件（Post Office Protocol 3）<br>
                <span class="protocol-box">IMAP</span> - 负责管理邮件（Internet Message Access Protocol）<br>
                <span class="protocol-box">HTTP</span> - 网页浏览协议（与邮件无直接关系）
            </p>
            
            <h3>🤔 做题思路</h3>
            <p>1. 看到"邮件发送" → 想到SMTP</p>
            <p>2. SMTP使用25端口（标准）或587端口（现代加密）</p>
            <p>3. POP3和IMAP是用来接收/管理邮件的</p>
            <p>4. HTTP是网页协议，与邮件传输无关</p>
            
            <p><strong>答案：B. SMTP</strong></p>
        </div>
    </div>

    <script>
        const canvas = document.getElementById('animationCanvas');
        const ctx = canvas.getContext('2d');
        
        // 设置canvas尺寸
        function resizeCanvas() {
            const rect = canvas.getBoundingClientRect();
            canvas.width = rect.width;
            canvas.height = rect.height;
        }
        
        resizeCanvas();
        window.addEventListener('resize', resizeCanvas);
        
        let animationStep = 0;
        let animationId;
        
        // 绘制函数
        function drawScene() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 绘制背景
            const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);
            gradient.addColorStop(0, '#e3f2fd');
            gradient.addColorStop(1, '#bbdefb');
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            
            if (animationStep >= 1) drawComputer();
            if (animationStep >= 2) drawServers();
            if (animationStep >= 3) drawConnections();
            if (animationStep >= 4) drawEmailFlow();
            if (animationStep >= 5) showProtocolLabels();
        }
        
        function drawComputer() {
            // 用户电脑
            ctx.fillStyle = '#2196F3';
            ctx.fillRect(50, 200, 80, 60);
            ctx.fillStyle = '#1976D2';
            ctx.fillRect(55, 205, 70, 40);
            
            // 屏幕
            ctx.fillStyle = '#E3F2FD';
            ctx.fillRect(60, 210, 60, 30);
            
            // 标签
            ctx.fillStyle = '#333';
            ctx.font = '14px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('用户电脑', 90, 280);
            ctx.fillText('邮件客户端', 90, 295);
        }
        
        function drawServers() {
            const servers = [
                {x: 300, y: 100, name: 'SMTP服务器', desc: '(发送邮件)', color: '#4CAF50'},
                {x: 300, y: 200, name: 'POP3服务器', desc: '(接收邮件)', color: '#FF9800'},
                {x: 300, y: 300, name: 'IMAP服务器', desc: '(管理邮件)', color: '#9C27B0'},
                {x: 500, y: 200, name: 'HTTP服务器', desc: '(网页服务)', color: '#607D8B'}
            ];
            
            servers.forEach(server => {
                // 服务器主体
                ctx.fillStyle = server.color;
                ctx.fillRect(server.x, server.y, 100, 80);
                ctx.fillStyle = 'rgba(255,255,255,0.3)';
                ctx.fillRect(server.x + 10, server.y + 10, 80, 60);
                
                // 标签
                ctx.fillStyle = '#333';
                ctx.font = '12px Arial';
                ctx.textAlign = 'center';
                ctx.fillText(server.name, server.x + 50, server.y + 95);
                ctx.fillText(server.desc, server.x + 50, server.y + 110);
            });
        }
        
        function drawConnections() {
            // 绘制连接线
            ctx.strokeStyle = '#666';
            ctx.lineWidth = 2;
            ctx.setLineDash([5, 5]);
            
            // 到各个服务器的连接
            const connections = [
                {x: 300, y: 140},  // SMTP
                {x: 300, y: 240},  // POP3
                {x: 300, y: 340},  // IMAP
                {x: 500, y: 240}   // HTTP
            ];
            
            connections.forEach(conn => {
                ctx.beginPath();
                ctx.moveTo(130, 230);
                ctx.lineTo(conn.x, conn.y);
                ctx.stroke();
            });
        }
        
        function drawEmailFlow() {
            // 突出显示SMTP连接
            ctx.strokeStyle = '#4CAF50';
            ctx.lineWidth = 4;
            ctx.setLineDash([]);
            
            ctx.beginPath();
            ctx.moveTo(130, 230);
            ctx.lineTo(300, 140);
            ctx.stroke();
            
            // 绘制邮件图标沿着SMTP路径移动
            const progress = (Date.now() % 2000) / 2000;
            const emailX = 130 + (170 * progress);
            const emailY = 230 - (90 * progress);
            
            ctx.fillStyle = '#FF5722';
            ctx.font = '20px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('📧', emailX, emailY);
            
            // 端口标签
            ctx.fillStyle = '#4CAF50';
            ctx.font = '14px Arial';
            ctx.fillText('端口25/587', 215, 160);
        }
        
        function showProtocolLabels() {
            // 显示协议说明
            ctx.fillStyle = 'rgba(255,255,255,0.9)';
            ctx.fillRect(450, 50, 200, 120);
            ctx.strokeStyle = '#4CAF50';
            ctx.lineWidth = 2;
            ctx.strokeRect(450, 50, 200, 120);
            
            ctx.fillStyle = '#333';
            ctx.font = '16px Arial';
            ctx.textAlign = 'left';
            ctx.fillText('邮件发送流程:', 460, 75);
            ctx.font = '12px Arial';
            ctx.fillText('1. 用户写邮件', 460, 95);
            ctx.fillText('2. 客户端连接SMTP', 460, 110);
            ctx.fillText('3. 通过25/587端口', 460, 125);
            ctx.fillText('4. 发送到目标邮箱', 460, 140);
            ctx.fillText('✅ 答案: SMTP', 460, 160);
        }
        
        function animate() {
            drawScene();
            animationId = requestAnimationFrame(animate);
        }
        
        function startAnimation() {
            animationStep = 0;
            clearInterval(window.stepInterval);
            
            window.stepInterval = setInterval(() => {
                animationStep++;
                if (animationStep > 5) {
                    clearInterval(window.stepInterval);
                    // 显示正确答案
                    setTimeout(() => {
                        document.querySelector('[data-answer="B"]').classList.add('correct');
                        document.querySelector('[data-answer="A"]').classList.add('wrong');
                    }, 1000);
                }
            }, 1500);
            
            animate();
        }
        
        // 选项点击事件
        document.querySelectorAll('.option').forEach(option => {
            option.addEventListener('click', function() {
                const answer = this.dataset.answer;
                
                // 清除之前的样式
                document.querySelectorAll('.option').forEach(opt => {
                    opt.classList.remove('correct', 'wrong');
                });
                
                if (answer === 'B') {
                    this.classList.add('correct');
                    setTimeout(() => {
                        alert('🎉 恭喜答对了！\n\nSMTP (Simple Mail Transfer Protocol) 是专门用于邮件发送的协议，使用25端口（标准）或587端口（加密）。');
                    }, 300);
                } else {
                    this.classList.add('wrong');
                    document.querySelector('[data-answer="B"]').classList.add('correct');
                    
                    let explanation = '';
                    switch(answer) {
                        case 'A':
                            explanation = 'POP3是用来接收邮件的协议，不是发送！';
                            break;
                        case 'C':
                            explanation = 'HTTP是网页浏览协议，与邮件传输无关！';
                            break;
                        case 'D':
                            explanation = 'IMAP是用来管理邮件的协议，不是发送！';
                            break;
                    }
                    
                    setTimeout(() => {
                        alert(`❌ 答错了！\n\n${explanation}\n\n正确答案是SMTP，专门负责邮件发送。`);
                    }, 300);
                }
            });
        });
        
        // 初始绘制
        drawScene();
    </script>
</body>
</html>
