<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>安全电子邮件服务学习</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/particles.js/2.0.0/particles.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            opacity: 0;
            transform: translateY(-50px);
        }

        .title {
            font-size: 3rem;
            color: white;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .subtitle {
            font-size: 1.2rem;
            color: rgba(255,255,255,0.8);
            margin-bottom: 30px;
        }

        .question-card {
            background: rgba(255,255,255,0.95);
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            opacity: 0;
            transform: translateY(50px);
        }

        .question-text {
            font-size: 1.5rem;
            color: #333;
            margin-bottom: 30px;
            line-height: 1.6;
        }

        .options-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .option {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            padding: 20px;
            border-radius: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
            font-size: 1.1rem;
            font-weight: bold;
            border: none;
            position: relative;
            overflow: hidden;
        }

        .option:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.2);
        }

        .option.correct {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            animation: pulse 2s infinite;
        }

        .option.wrong {
            background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
            animation: shake 0.5s ease-in-out;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }

        .explanation-section {
            background: rgba(255,255,255,0.95);
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            opacity: 0;
            transform: translateY(50px);
        }

        .concept-card {
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
            border-radius: 15px;
            padding: 30px;
            margin: 20px 0;
            position: relative;
            overflow: hidden;
        }

        .concept-title {
            font-size: 1.5rem;
            color: #333;
            margin-bottom: 15px;
            font-weight: bold;
        }

        .concept-description {
            font-size: 1.1rem;
            color: #555;
            line-height: 1.6;
        }

        .animation-canvas {
            width: 100%;
            height: 300px;
            border-radius: 15px;
            margin: 20px 0;
            background: rgba(255,255,255,0.1);
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: rgba(255,255,255,0.3);
            border-radius: 4px;
            margin: 20px 0;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #4facfe 0%, #00f2fe 100%);
            width: 0%;
            transition: width 1s ease;
        }

        .next-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 25px;
            font-size: 1.1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            display: none;
        }

        .next-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .particles-js {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
        }

        .floating-icon {
            position: absolute;
            font-size: 2rem;
            opacity: 0.7;
            animation: float 3s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }
    </style>
</head>
<body>
    <div id="particles-js" class="particles-js"></div>
    
    <div class="container">
        <div class="header">
            <h1 class="title">🔐 安全电子邮件服务</h1>
            <p class="subtitle">通过动画和交互学习网络安全知识</p>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
        </div>

        <div class="question-card" id="questionCard">
            <h2 class="question-text">下面可提供安全电子邮件服务的是（ ）？</h2>
            <div class="options-container">
                <button class="option" data-option="A" onclick="selectOption('A')">
                    A. RSA
                </button>
                <button class="option" data-option="B" onclick="selectOption('B')">
                    B. SSL
                </button>
                <button class="option" data-option="C" onclick="selectOption('C')">
                    C. SET
                </button>
                <button class="option" data-option="D" onclick="selectOption('D')">
                    D. S/MIME
                </button>
            </div>
            <button class="next-btn" id="nextBtn" onclick="showExplanation()">查看详细解释 →</button>
        </div>

        <div class="explanation-section" id="explanationSection">
            <h2 style="text-align: center; color: #333; margin-bottom: 30px;">📚 知识点详解</h2>
            
            <div class="concept-card" id="conceptCard1">
                <div class="floating-icon" style="top: 10px; right: 20px;">🔒</div>
                <h3 class="concept-title">什么是电子邮件安全？</h3>
                <p class="concept-description">
                    电子邮件安全是保护邮件内容不被窃取、篡改或伪造的技术。就像给信件加上密码锁一样！
                </p>
                <canvas class="animation-canvas" id="emailCanvas"></canvas>
            </div>

            <div class="concept-card" id="conceptCard2">
                <div class="floating-icon" style="top: 10px; right: 20px;">📧</div>
                <h3 class="concept-title">S/MIME - 安全多用途互联网邮件扩展</h3>
                <p class="concept-description">
                    S/MIME是专门为电子邮件设计的安全协议，可以对邮件进行数字签名和加密，确保邮件的真实性和保密性。
                </p>
                <canvas class="animation-canvas" id="smimeCanvas"></canvas>
            </div>

            <div class="concept-card" id="conceptCard3">
                <div class="floating-icon" style="top: 10px; right: 20px;">🔑</div>
                <h3 class="concept-title">其他选项分析</h3>
                <p class="concept-description">
                    RSA是加密算法，SSL是传输层安全协议，SET是电子商务安全协议，它们都不是专门的邮件安全服务。
                </p>
                <canvas class="animation-canvas" id="comparisonCanvas"></canvas>
            </div>

            <button class="next-btn" id="finalBtn" onclick="showSummary()" style="display: block; margin: 30px auto;">
                总结要点 🎯
            </button>
        </div>
    </div>

    <script>
        // 粒子背景效果
        particlesJS('particles-js', {
            particles: {
                number: { value: 80, density: { enable: true, value_area: 800 } },
                color: { value: "#ffffff" },
                shape: { type: "circle" },
                opacity: { value: 0.5, random: false },
                size: { value: 3, random: true },
                line_linked: { enable: true, distance: 150, color: "#ffffff", opacity: 0.4, width: 1 },
                move: { enable: true, speed: 6, direction: "none", random: false, straight: false, out_mode: "out", bounce: false }
            },
            interactivity: {
                detect_on: "canvas",
                events: { onhover: { enable: true, mode: "repulse" }, onclick: { enable: true, mode: "push" }, resize: true },
                modes: { grab: { distance: 400, line_linked: { opacity: 1 } }, bubble: { distance: 400, size: 40, duration: 2, opacity: 8, speed: 3 }, repulse: { distance: 200, duration: 0.4 }, push: { particles_nb: 4 }, remove: { particles_nb: 2 } }
            },
            retina_detect: true
        });

        let currentStep = 0;
        let selectedAnswer = null;

        // 初始化动画
        function initAnimations() {
            gsap.to('.header', { opacity: 1, y: 0, duration: 1, ease: 'power2.out' });
            gsap.to('.question-card', { opacity: 1, y: 0, duration: 1, delay: 0.3, ease: 'power2.out' });
            gsap.to('#progressFill', { width: '25%', duration: 1, delay: 0.5 });
        }

        // 选择答案
        function selectOption(option) {
            selectedAnswer = option;
            const options = document.querySelectorAll('.option');
            
            options.forEach(opt => {
                opt.style.pointerEvents = 'none';
                if (opt.dataset.option === option) {
                    if (option === 'D') {
                        opt.classList.add('correct');
                    } else {
                        opt.classList.add('wrong');
                    }
                }
            });

            // 显示正确答案
            setTimeout(() => {
                const correctOption = document.querySelector('[data-option="D"]');
                correctOption.classList.add('correct');
                document.getElementById('nextBtn').style.display = 'block';
                gsap.to('#progressFill', { width: '50%', duration: 1 });
            }, 1000);
        }

        // 显示解释
        function showExplanation() {
            gsap.to('#explanationSection', { opacity: 1, y: 0, duration: 1, ease: 'power2.out' });
            gsap.to('#progressFill', { width: '75%', duration: 1 });
            
            // 启动动画
            setTimeout(() => {
                animateEmailSecurity();
                animateSMIME();
                animateComparison();
            }, 500);
        }

        // 邮件安全动画
        function animateEmailSecurity() {
            const canvas = document.getElementById('emailCanvas');
            const ctx = canvas.getContext('2d');
            canvas.width = canvas.offsetWidth;
            canvas.height = canvas.offsetHeight;

            let frame = 0;
            function animate() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                
                // 绘制邮件图标
                ctx.fillStyle = '#4facfe';
                ctx.fillRect(50 + Math.sin(frame * 0.1) * 10, 100, 100, 80);
                
                // 绘制锁图标
                ctx.fillStyle = '#f5576c';
                ctx.beginPath();
                ctx.arc(200 + Math.cos(frame * 0.1) * 10, 140, 20, 0, Math.PI * 2);
                ctx.fill();
                
                // 绘制连接线
                ctx.strokeStyle = '#667eea';
                ctx.lineWidth = 3;
                ctx.beginPath();
                ctx.moveTo(150, 140);
                ctx.lineTo(180, 140);
                ctx.stroke();
                
                frame++;
                if (frame < 200) {
                    requestAnimationFrame(animate);
                }
            }
            animate();
        }

        // S/MIME动画
        function animateSMIME() {
            const canvas = document.getElementById('smimeCanvas');
            const ctx = canvas.getContext('2d');
            canvas.width = canvas.offsetWidth;
            canvas.height = canvas.offsetHeight;

            let frame = 0;
            function animate() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                
                // 绘制发送者
                ctx.fillStyle = '#a8edea';
                ctx.fillRect(50, 50, 80, 60);
                ctx.fillStyle = '#333';
                ctx.font = '12px Arial';
                ctx.fillText('发送者', 60, 85);
                
                // 绘制加密过程
                const progress = (frame % 100) / 100;
                ctx.fillStyle = `rgba(245, 87, 108, ${progress})`;
                ctx.fillRect(150 + progress * 100, 70, 20, 20);
                
                // 绘制接收者
                ctx.fillStyle = '#fed6e3';
                ctx.fillRect(300, 50, 80, 60);
                ctx.fillStyle = '#333';
                ctx.fillText('接收者', 310, 85);
                
                frame++;
                if (frame < 300) {
                    requestAnimationFrame(animate);
                }
            }
            animate();
        }

        // 对比动画
        function animateComparison() {
            const canvas = document.getElementById('comparisonCanvas');
            const ctx = canvas.getContext('2d');
            canvas.width = canvas.offsetWidth;
            canvas.height = canvas.offsetHeight;

            const protocols = [
                { name: 'RSA', color: '#ff9a9e', x: 50, purpose: '加密算法' },
                { name: 'SSL', color: '#fecfef', x: 150, purpose: '传输安全' },
                { name: 'SET', color: '#a8edea', x: 250, purpose: '电商安全' },
                { name: 'S/MIME', color: '#4facfe', x: 350, purpose: '邮件安全' }
            ];

            let frame = 0;
            function animate() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                
                protocols.forEach((protocol, index) => {
                    const delay = index * 20;
                    const alpha = Math.max(0, Math.min(1, (frame - delay) / 50));
                    
                    ctx.fillStyle = protocol.color;
                    ctx.globalAlpha = alpha;
                    ctx.fillRect(protocol.x, 50, 80, 60);
                    
                    ctx.fillStyle = '#333';
                    ctx.font = '14px Arial';
                    ctx.fillText(protocol.name, protocol.x + 10, 85);
                    ctx.font = '10px Arial';
                    ctx.fillText(protocol.purpose, protocol.x + 5, 100);
                    
                    if (protocol.name === 'S/MIME' && alpha > 0.5) {
                        ctx.strokeStyle = '#f5576c';
                        ctx.lineWidth = 3;
                        ctx.strokeRect(protocol.x - 5, 45, 90, 70);
                    }
                });
                
                ctx.globalAlpha = 1;
                frame++;
                if (frame < 200) {
                    requestAnimationFrame(animate);
                }
            }
            animate();
        }

        // 显示总结
        function showSummary() {
            gsap.to('#progressFill', { width: '100%', duration: 1 });
            
            const summary = `
                🎯 学习总结：
                
                ✅ S/MIME是专门的安全电子邮件服务
                ✅ 提供数字签名和加密功能
                ✅ 确保邮件的真实性和保密性
                ✅ 其他选项都不是专门的邮件安全服务
                
                恭喜你掌握了这个知识点！🎉
            `;
            
            alert(summary);
        }

        // 页面加载完成后初始化
        window.addEventListener('load', initAnimations);
    </script>
</body>
</html>
