<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DNS PTR记录学习 - 反向解析原理</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .title {
            text-align: center;
            color: white;
            font-size: 2.5rem;
            margin-bottom: 60px;
            opacity: 0;
            transform: translateY(-50px);
        }

        .section {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            opacity: 0;
            transform: translateY(50px);
        }

        .section h2 {
            color: #4a5568;
            font-size: 1.8rem;
            margin-bottom: 30px;
            text-align: center;
        }

        .question-box {
            background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
            padding: 30px;
            border-radius: 15px;
            margin-bottom: 30px;
            color: #2d3748;
            font-size: 1.1rem;
            line-height: 1.8;
        }

        .canvas-container {
            display: flex;
            justify-content: center;
            margin: 30px 0;
        }

        canvas {
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            background: white;
        }

        .interactive-demo {
            display: flex;
            justify-content: space-around;
            align-items: center;
            margin: 40px 0;
            flex-wrap: wrap;
            gap: 20px;
        }

        .demo-button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1.1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        .demo-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
        }

        .explanation {
            background: #f7fafc;
            padding: 25px;
            border-radius: 15px;
            margin: 20px 0;
            border-left: 5px solid #667eea;
        }

        .step-indicator {
            display: flex;
            justify-content: center;
            margin: 30px 0;
            gap: 20px;
        }

        .step {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #e2e8f0;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            color: #4a5568;
            transition: all 0.3s ease;
        }

        .step.active {
            background: #667eea;
            color: white;
            transform: scale(1.2);
        }

        .answer-options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }

        .option {
            background: white;
            border: 2px solid #e2e8f0;
            border-radius: 15px;
            padding: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
        }

        .option:hover {
            border-color: #667eea;
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }

        .option.correct {
            border-color: #48bb78;
            background: #f0fff4;
        }

        .option.wrong {
            border-color: #f56565;
            background: #fff5f5;
        }

        .thinking-process {
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
            padding: 25px;
            border-radius: 15px;
            margin: 20px 0;
        }

        .thinking-step {
            margin: 15px 0;
            padding: 15px;
            background: rgba(255, 255, 255, 0.8);
            border-radius: 10px;
            border-left: 4px solid #ed8936;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .pulse {
            animation: pulse 2s infinite;
        }

        .highlight {
            background: linear-gradient(120deg, #a8edea 0%, #fed6e3 100%);
            padding: 2px 8px;
            border-radius: 5px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="title">🌐 DNS PTR记录学习</h1>
        
        <div class="section" id="question-section">
            <h2>📝 题目分析</h2>
            <div class="question-box">
                <strong>题目：</strong>在客户机上运行nslookup查询某服务器名称时能解析出IP地址，查询IP地址时却不能解析出服务器名称，解决这一问题的方法是（ ）。
                <br><br>
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin-top: 20px;">
                    <div style="background: rgba(255,255,255,0.7); padding: 15px; border-radius: 10px;">
                        <strong>A.</strong> 清除DNS缓存
                    </div>
                    <div style="background: rgba(255,255,255,0.7); padding: 15px; border-radius: 10px;">
                        <strong>B.</strong> 刷新DNS缓存
                    </div>
                    <div style="background: rgba(255,255,255,0.7); padding: 15px; border-radius: 10px;">
                        <strong>C.</strong> 为该服务器创建PTR记录
                    </div>
                    <div style="background: rgba(255,255,255,0.7); padding: 15px; border-radius: 10px;">
                        <strong>D.</strong> 重启DNS服务
                    </div>
                </div>
            </div>
        </div>

        <div class="section" id="thinking-section">
            <h2>🤔 解题思路分析</h2>
            <div class="thinking-process">
                <div class="thinking-step" id="think1" style="opacity: 0;">
                    <strong>第1步：理解题目现象</strong><br>
                    • <span class="highlight">正向查询正常</span>：域名 → IP地址 ✅<br>
                    • <span class="highlight">反向查询失败</span>：IP地址 → 域名 ❌
                </div>
                <div class="thinking-step" id="think2" style="opacity: 0;">
                    <strong>第2步：分析问题原因</strong><br>
                    既然正向查询正常，说明DNS服务器工作正常，A记录存在<br>
                    反向查询失败，说明缺少反向解析记录
                </div>
                <div class="thinking-step" id="think3" style="opacity: 0;">
                    <strong>第3步：确定解决方案</strong><br>
                    反向查询需要 <span class="highlight">PTR记录</span> 来建立IP到域名的映射
                </div>
                <div class="thinking-step" id="think4" style="opacity: 0;">
                    <strong>第4步：排除其他选项</strong><br>
                    • A/B选项：缓存问题不能解决记录不存在的问题<br>
                    • D选项：DNS服务正常运行，重启无效
                </div>
            </div>
            <button class="demo-button" onclick="startThinking()">开始思考过程</button>
        </div>

        <div class="section" id="concept-section">
            <h2>🎯 DNS记录类型学习</h2>
            <div class="step-indicator">
                <div class="step active" id="step1">A</div>
                <div class="step" id="step2">PTR</div>
                <div class="step" id="step3">对比</div>
                <div class="step" id="step4">实战</div>
            </div>
            
            <div class="canvas-container">
                <canvas id="conceptCanvas" width="900" height="500"></canvas>
            </div>
            
            <div class="interactive-demo">
                <button class="demo-button" onclick="showARecord()">A记录演示</button>
                <button class="demo-button" onclick="showPTRRecord()">PTR记录演示</button>
                <button class="demo-button" onclick="showComparison()">对比分析</button>
                <button class="demo-button" onclick="showSolution()">解决方案</button>
            </div>
        </div>

        <div class="section" id="explanation-section">
            <h2>💡 详细解释</h2>
            <div class="explanation" id="explanation-content">
                点击上方按钮开始学习DNS记录的工作原理！
            </div>
        </div>

        <div class="section" id="answer-section">
            <h2>🎮 互动答题</h2>
            <div class="answer-options">
                <div class="option" onclick="selectAnswer('A')">
                    <strong>A</strong><br>清除DNS缓存<br>
                    <small>清理本地缓存数据</small>
                </div>
                <div class="option" onclick="selectAnswer('B')">
                    <strong>B</strong><br>刷新DNS缓存<br>
                    <small>更新缓存内容</small>
                </div>
                <div class="option" onclick="selectAnswer('C')">
                    <strong>C</strong><br>为该服务器创建PTR记录<br>
                    <small>建立反向解析映射</small>
                </div>
                <div class="option" onclick="selectAnswer('D')">
                    <strong>D</strong><br>重启DNS服务<br>
                    <small>重新启动DNS服务器</small>
                </div>
            </div>
        </div>

        <div class="section" id="practice-section">
            <h2>🧪 实战练习</h2>
            <div class="explanation">
                <h3>模拟nslookup命令</h3>
                <p>体验一下实际的DNS查询过程：</p>
                <div style="display: flex; gap: 20px; margin: 20px 0; flex-wrap: wrap;">
                    <button class="demo-button" onclick="simulateForward()">nslookup www.example.com</button>
                    <button class="demo-button" onclick="simulateReverse()">nslookup *************</button>
                </div>
                <div id="terminal-output" style="background: #2d3748; color: #e2e8f0; padding: 20px; border-radius: 10px; font-family: 'Courier New', monospace; min-height: 100px; margin: 20px 0;">
                    点击上方按钮模拟DNS查询...
                </div>
            </div>
        </div>

        <div class="section" id="summary-section">
            <h2>📚 知识总结</h2>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px;">
                <div class="explanation">
                    <h3>🔍 DNS记录类型</h3>
                    <p><strong>A记录：</strong>域名 → IPv4地址</p>
                    <p><strong>PTR记录：</strong>IP地址 → 域名</p>
                    <p><strong>CNAME记录：</strong>域名别名</p>
                    <p><strong>MX记录：</strong>邮件服务器</p>
                </div>
                <div class="explanation">
                    <h3>🛠️ 故障排查思路</h3>
                    <p>1. 分析现象（哪个方向查询失败）</p>
                    <p>2. 确定缺失的记录类型</p>
                    <p>3. 排除无关因素</p>
                    <p>4. 选择正确的解决方案</p>
                </div>
                <div class="explanation">
                    <h3>💡 记忆技巧</h3>
                    <p><strong>正向查询：</strong>像查电话簿，知道姓名找电话</p>
                    <p><strong>反向查询：</strong>像查来电显示，知道电话找姓名</p>
                    <p><strong>PTR记录：</strong>Pointer = 指针，指向域名</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 初始化动画
        gsap.to('.title', {duration: 1, opacity: 1, y: 0, ease: 'back.out(1.7)'});
        gsap.to('.section', {duration: 0.8, opacity: 1, y: 0, stagger: 0.2, delay: 0.5});

        const canvas = document.getElementById('conceptCanvas');
        const ctx = canvas.getContext('2d');
        let currentStep = 1;

        // 思考过程动画
        function startThinking() {
            for(let i = 1; i <= 4; i++) {
                setTimeout(() => {
                    gsap.to(`#think${i}`, {duration: 0.8, opacity: 1, x: 0, ease: 'back.out(1.7)'});
                }, (i-1) * 1000);
            }
        }

        // 绘制基础场景
        function drawScene() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 绘制客户端
            drawComputer(50, 250, '客户端', '#4299e1');
            
            // 绘制DNS服务器
            drawServer(400, 200, 'DNS服务器', '#48bb78');
            
            // 绘制目标服务器
            drawServer(750, 250, '目标服务器\nwww.example.com\n*************', '#ed8936');
        }

        function drawComputer(x, y, label, color) {
            // 电脑主体
            ctx.fillStyle = color;
            ctx.fillRect(x, y, 100, 80);
            ctx.fillStyle = '#2d3748';
            ctx.fillRect(x + 15, y + 15, 70, 45);
            
            // 显示器支架
            ctx.fillStyle = color;
            ctx.fillRect(x + 45, y + 80, 10, 25);
            ctx.fillRect(x + 25, y + 105, 50, 10);
            
            // 标签
            ctx.fillStyle = '#2d3748';
            ctx.font = '16px Arial';
            ctx.textAlign = 'center';
            ctx.fillText(label, x + 50, y + 135);
        }

        function drawServer(x, y, label, color) {
            // 服务器主体
            ctx.fillStyle = color;
            ctx.fillRect(x, y, 120, 100);
            
            // 服务器细节
            ctx.fillStyle = '#2d3748';
            for(let i = 0; i < 4; i++) {
                ctx.fillRect(x + 15, y + 15 + i * 20, 90, 15);
            }
            
            // 指示灯
            ctx.fillStyle = '#48bb78';
            ctx.beginPath();
            ctx.arc(x + 100, y + 22, 4, 0, 2 * Math.PI);
            ctx.fill();
            
            // 标签
            ctx.fillStyle = '#2d3748';
            ctx.font = '14px Arial';
            ctx.textAlign = 'center';
            const lines = label.split('\n');
            lines.forEach((line, index) => {
                ctx.fillText(line, x + 60, y + 125 + index * 18);
            });
        }

        function drawArrow(fromX, fromY, toX, toY, label, color = '#667eea', animated = false) {
            ctx.strokeStyle = color;
            ctx.lineWidth = 4;
            ctx.beginPath();
            ctx.moveTo(fromX, fromY);
            ctx.lineTo(toX, toY);
            ctx.stroke();
            
            // 箭头头部
            const angle = Math.atan2(toY - fromY, toX - fromX);
            ctx.beginPath();
            ctx.moveTo(toX, toY);
            ctx.lineTo(toX - 20 * Math.cos(angle - Math.PI/6), toY - 20 * Math.sin(angle - Math.PI/6));
            ctx.lineTo(toX - 20 * Math.cos(angle + Math.PI/6), toY - 20 * Math.sin(angle + Math.PI/6));
            ctx.closePath();
            ctx.fillStyle = color;
            ctx.fill();
            
            // 标签
            if(label) {
                ctx.fillStyle = '#2d3748';
                ctx.font = '14px Arial';
                ctx.textAlign = 'center';
                const midX = (fromX + toX) / 2;
                const midY = (fromY + toY) / 2 - 15;
                
                // 背景
                const textWidth = ctx.measureText(label).width;
                ctx.fillStyle = 'rgba(255, 255, 255, 0.9)';
                ctx.fillRect(midX - textWidth/2 - 5, midY - 15, textWidth + 10, 20);
                
                ctx.fillStyle = '#2d3748';
                ctx.fillText(label, midX, midY);
            }
        }

        function drawRecord(x, y, type, content, color) {
            // 记录框
            ctx.fillStyle = color;
            ctx.fillRect(x, y, 200, 60);
            ctx.fillStyle = 'white';
            ctx.font = 'bold 16px Arial';
            ctx.textAlign = 'center';
            ctx.fillText(`${type} 记录`, x + 100, y + 20);
            ctx.font = '12px Arial';
            ctx.fillText(content, x + 100, y + 40);
        }

        // A记录演示
        function showARecord() {
            updateStep(1);
            drawScene();
            
            // 绘制A记录
            drawRecord(350, 80, 'A', 'www.example.com → *************', '#4299e1');
            
            setTimeout(() => {
                drawArrow(150, 290, 400, 250, '查询: www.example.com?', '#4299e1');
            }, 500);
            
            setTimeout(() => {
                drawArrow(400, 270, 150, 310, '返回: *************', '#48bb78');
            }, 1500);
            
            updateExplanation(`
                <h3>📋 A记录 (Address Record)</h3>
                <p><strong>作用：</strong>将域名映射到IPv4地址</p>
                <p><strong>格式：</strong><code>域名 → IP地址</code></p>
                <p><strong>示例：</strong><code>www.example.com → *************</code></p>
                <br>
                <p><strong>工作过程：</strong></p>
                <p>1. 客户端查询："www.example.com 的IP是什么？"</p>
                <p>2. DNS服务器查找A记录</p>
                <p>3. 返回IP地址：*************</p>
                <br>
                <p style="color: #48bb78;">✅ 正向查询成功！</p>
            `);
        }

        // PTR记录演示
        function showPTRRecord() {
            updateStep(2);
            drawScene();
            
            // 绘制PTR记录（缺失状态）
            ctx.fillStyle = '#f56565';
            ctx.fillRect(350, 80, 200, 60);
            ctx.fillStyle = 'white';
            ctx.font = 'bold 16px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('PTR 记录', 450, 100);
            ctx.fillText('❌ 不存在', 450, 120);
            
            setTimeout(() => {
                drawArrow(150, 290, 400, 250, '查询: *************?', '#f56565');
            }, 500);
            
            setTimeout(() => {
                ctx.fillStyle = '#f56565';
                ctx.font = '18px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('❌ 查询失败', 300, 180);
            }, 1500);
            
            setTimeout(() => {
                // 显示正确的PTR记录
                ctx.fillStyle = '#48bb78';
                ctx.fillRect(350, 350, 200, 60);
                ctx.fillStyle = 'white';
                ctx.font = 'bold 16px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('PTR 记录', 450, 370);
                ctx.font = '12px Arial';
                ctx.fillText('************* → www.example.com', 450, 390);
                
                ctx.fillStyle = '#48bb78';
                ctx.font = '16px Arial';
                ctx.fillText('✅ 创建PTR记录后', 450, 330);
            }, 3000);
            
            updateExplanation(`
                <h3>🔄 PTR记录 (Pointer Record)</h3>
                <p><strong>作用：</strong>将IP地址映射到域名（反向解析）</p>
                <p><strong>格式：</strong><code>IP地址 → 域名</code></p>
                <p><strong>示例：</strong><code>************* → www.example.com</code></p>
                <br>
                <p><strong>问题分析：</strong></p>
                <p>• 题目中反向查询失败 = PTR记录不存在</p>
                <p>• 正向查询正常 = A记录存在且DNS服务正常</p>
                <br>
                <p><strong>解决方案：</strong></p>
                <p style="color: #48bb78; font-weight: bold;">创建PTR记录建立IP到域名的映射关系</p>
            `);
        }

        // 对比分析
        function showComparison() {
            updateStep(3);
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 绘制对比图
            ctx.fillStyle = '#4299e1';
            ctx.fillRect(50, 100, 350, 150);
            ctx.fillStyle = 'white';
            ctx.font = 'bold 20px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('正向查询 (A记录)', 225, 130);
            ctx.font = '16px Arial';
            ctx.fillText('域名 → IP地址', 225, 160);
            ctx.fillText('www.example.com → *************', 225, 185);
            ctx.fillText('✅ 正常工作', 225, 210);
            
            ctx.fillStyle = '#ed8936';
            ctx.fillRect(500, 100, 350, 150);
            ctx.fillStyle = 'white';
            ctx.font = 'bold 20px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('反向查询 (PTR记录)', 675, 130);
            ctx.font = '16px Arial';
            ctx.fillText('IP地址 → 域名', 675, 160);
            ctx.fillText('************* → www.example.com', 675, 185);
            ctx.fillText('❌ 记录缺失', 675, 210);
            
            // 绘制箭头
            drawArrow(225, 280, 225, 320, '', '#4299e1');
            drawArrow(675, 280, 675, 320, '', '#ed8936');
            
            // 结论
            ctx.fillStyle = '#48bb78';
            ctx.fillRect(300, 350, 300, 100);
            ctx.fillStyle = 'white';
            ctx.font = 'bold 18px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('解决方案', 450, 380);
            ctx.font = '16px Arial';
            ctx.fillText('创建PTR记录', 450, 410);
            ctx.fillText('建立反向映射', 450, 430);
            
            updateExplanation(`
                <h3>⚖️ 正向查询 vs 反向查询</h3>
                <table style="width: 100%; border-collapse: collapse; margin: 20px 0;">
                    <tr style="background: #f7fafc;">
                        <th style="padding: 15px; border: 1px solid #e2e8f0;">查询类型</th>
                        <th style="padding: 15px; border: 1px solid #e2e8f0;">记录类型</th>
                        <th style="padding: 15px; border: 1px solid #e2e8f0;">查询方向</th>
                        <th style="padding: 15px; border: 1px solid #e2e8f0;">题目状态</th>
                    </tr>
                    <tr>
                        <td style="padding: 15px; border: 1px solid #e2e8f0;">正向查询</td>
                        <td style="padding: 15px; border: 1px solid #e2e8f0;">A记录</td>
                        <td style="padding: 15px; border: 1px solid #e2e8f0;">域名 → IP</td>
                        <td style="padding: 15px; border: 1px solid #e2e8f0; color: #48bb78;">✅ 正常</td>
                    </tr>
                    <tr>
                        <td style="padding: 15px; border: 1px solid #e2e8f0;">反向查询</td>
                        <td style="padding: 15px; border: 1px solid #e2e8f0;">PTR记录</td>
                        <td style="padding: 15px; border: 1px solid #e2e8f0;">IP → 域名</td>
                        <td style="padding: 15px; border: 1px solid #e2e8f0; color: #f56565;">❌ 失败</td>
                    </tr>
                </table>
                <p><strong>结论：</strong>需要创建PTR记录来解决反向查询问题</p>
            `);
        }

        // 解决方案演示
        function showSolution() {
            updateStep(4);
            drawScene();
            
            // 显示解决步骤
            setTimeout(() => {
                ctx.fillStyle = '#805ad5';
                ctx.fillRect(350, 50, 200, 80);
                ctx.fillStyle = 'white';
                ctx.font = 'bold 16px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('创建PTR记录', 450, 75);
                ctx.font = '12px Arial';
                ctx.fillText('*************', 450, 95);
                ctx.fillText('↓', 450, 110);
                ctx.fillText('www.example.com', 450, 125);
            }, 500);
            
            setTimeout(() => {
                drawArrow(150, 290, 400, 250, '查询: *************?', '#4299e1');
            }, 1000);
            
            setTimeout(() => {
                drawArrow(400, 270, 150, 310, '返回: www.example.com', '#48bb78');
            }, 2000);
            
            setTimeout(() => {
                ctx.fillStyle = '#48bb78';
                ctx.font = 'bold 20px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('🎉 反向查询成功！', 450, 400);
            }, 2500);
            
            updateExplanation(`
                <h3>🛠️ 完整解决方案</h3>
                <p><strong>问题诊断：</strong></p>
                <p>• 正向查询正常 → DNS服务器和A记录都工作正常</p>
                <p>• 反向查询失败 → 缺少PTR记录</p>
                <br>
                <p><strong>解决步骤：</strong></p>
                <p>1. 在DNS服务器上创建PTR记录</p>
                <p>2. 配置IP地址到域名的映射：<code>************* → www.example.com</code></p>
                <p>3. 重新加载DNS配置</p>
                <p>4. 测试反向查询：<code>nslookup *************</code></p>
                <br>
                <p><strong>为什么其他选项不对？</strong></p>
                <p>• <strong>A/B选项</strong>：缓存操作无法创建不存在的记录</p>
                <p>• <strong>D选项</strong>：DNS服务本身正常，重启无效</p>
                <br>
                <p style="color: #48bb78; font-weight: bold; font-size: 1.2em;">答案：C - 为该服务器创建PTR记录</p>
            `);
        }

        function updateStep(step) {
            document.querySelectorAll('.step').forEach(s => s.classList.remove('active'));
            if(step === 1) document.getElementById('step1').classList.add('active');
            else if(step === 2) document.getElementById('step2').classList.add('active');
            else if(step === 3) document.getElementById('step3').classList.add('active');
            else if(step === 4) document.getElementById('step4').classList.add('active');
            currentStep = step;
        }

        function updateExplanation(content) {
            document.getElementById('explanation-content').innerHTML = content;
            gsap.from('#explanation-content', {duration: 0.5, opacity: 0, y: 20});
        }

        function selectAnswer(option) {
            const options = document.querySelectorAll('.option');
            options.forEach(opt => {
                opt.classList.remove('correct', 'wrong');
            });
            
            if(option === 'C') {
                event.target.classList.add('correct');
                gsap.to(event.target, {duration: 0.5, scale: 1.05, yoyo: true, repeat: 1});
                
                setTimeout(() => {
                    alert('🎉 恭喜答对了！\n\n正确答案是C：为该服务器创建PTR记录\n\n解释：\n• 正向查询正常说明A记录存在，DNS服务正常\n• 反向查询失败说明缺少PTR记录\n• PTR记录专门用于IP地址到域名的反向解析\n• 创建PTR记录后，反向查询就能正常工作了');
                }, 500);
            } else {
                event.target.classList.add('wrong');
                gsap.to(event.target, {duration: 0.2, x: -10, yoyo: true, repeat: 3});
                
                let hint = '';
                if(option === 'A' || option === 'B') {
                    hint = '缓存操作无法解决记录不存在的问题。既然正向查询正常，说明DNS服务和记录都是好的，问题在于缺少特定类型的记录。';
                } else if(option === 'D') {
                    hint = 'DNS服务本身是正常的（正向查询成功），重启服务不能创建缺失的记录。';
                }
                
                setTimeout(() => {
                    alert(`❌ 答案不正确\n\n提示：${hint}\n\n再想想：什么记录类型专门用于IP地址到域名的反向解析？`);
                }, 500);
            }
        }

        // 模拟nslookup命令
        function simulateForward() {
            const output = document.getElementById('terminal-output');
            output.innerHTML = '';

            // 模拟命令输入
            typeText(output, '> nslookup www.example.com\n', 50, () => {
                typeText(output, 'Server: *******\n', 30, () => {
                    typeText(output, 'Address: *******#53\n\n', 30, () => {
                        typeText(output, 'Non-authoritative answer:\n', 30, () => {
                            typeText(output, 'Name: www.example.com\n', 30, () => {
                                typeText(output, 'Address: *************\n\n', 30, () => {
                                    typeText(output, '✅ 正向查询成功！域名解析为IP地址', 30);
                                });
                            });
                        });
                    });
                });
            });
        }

        function simulateReverse() {
            const output = document.getElementById('terminal-output');
            output.innerHTML = '';

            // 模拟命令输入
            typeText(output, '> nslookup *************\n', 50, () => {
                typeText(output, 'Server: *******\n', 30, () => {
                    typeText(output, 'Address: *******#53\n\n', 30, () => {
                        typeText(output, '** server can\'t find *************.in-addr.arpa: NXDOMAIN\n\n', 30, () => {
                            typeText(output, '❌ 反向查询失败！缺少PTR记录\n\n', 30, () => {
                                typeText(output, '💡 解决方案：创建PTR记录\n', 30, () => {
                                    typeText(output, '   ************* → www.example.com', 30);
                                });
                            });
                        });
                    });
                });
            });
        }

        function typeText(element, text, speed, callback) {
            let i = 0;
            const timer = setInterval(() => {
                if (i < text.length) {
                    element.innerHTML += text.charAt(i);
                    i++;
                } else {
                    clearInterval(timer);
                    if (callback) callback();
                }
            }, speed);
        }

        // 初始化
        drawScene();

        // 自动开始演示
        setTimeout(() => {
            showARecord();
        }, 2000);
    </script>
</body>
</html>
