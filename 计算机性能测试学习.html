<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>计算机性能测试 - 基准测试学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            opacity: 0;
            animation: fadeInUp 1s ease-out forwards;
        }

        .title {
            font-size: 3rem;
            color: white;
            margin-bottom: 20px;
            text-shadow: 0 4px 8px rgba(0,0,0,0.3);
        }

        .subtitle {
            font-size: 1.2rem;
            color: rgba(255,255,255,0.8);
            margin-bottom: 40px;
        }

        .section {
            background: white;
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            opacity: 0;
            transform: translateY(30px);
            animation: fadeInUp 1s ease-out forwards;
        }

        .section:nth-child(2) { animation-delay: 0.2s; }
        .section:nth-child(3) { animation-delay: 0.4s; }
        .section:nth-child(4) { animation-delay: 0.6s; }

        .section-title {
            font-size: 2rem;
            color: #333;
            margin-bottom: 30px;
            text-align: center;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 2px;
        }

        .canvas-container {
            display: flex;
            justify-content: center;
            margin: 30px 0;
        }

        canvas {
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            cursor: pointer;
            transition: transform 0.3s ease;
        }

        canvas:hover {
            transform: scale(1.02);
        }

        .explanation {
            font-size: 1.1rem;
            line-height: 1.8;
            color: #555;
            margin: 20px 0;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
            border-left: 4px solid #667eea;
        }

        .quiz-section {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
        }

        .quiz-section .section-title {
            color: white;
        }

        .quiz-section .section-title::after {
            background: white;
        }

        .options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }

        .option {
            background: rgba(255,255,255,0.1);
            border: 2px solid rgba(255,255,255,0.3);
            border-radius: 15px;
            padding: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
            font-size: 1.1rem;
        }

        .option:hover {
            background: rgba(255,255,255,0.2);
            transform: translateY(-5px);
        }

        .option.correct {
            background: rgba(76, 175, 80, 0.3);
            border-color: #4CAF50;
            animation: pulse 0.6s ease-in-out;
        }

        .option.wrong {
            background: rgba(244, 67, 54, 0.3);
            border-color: #f44336;
            animation: shake 0.6s ease-in-out;
        }

        .result {
            margin-top: 30px;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            font-size: 1.2rem;
            opacity: 0;
            transform: translateY(20px);
            transition: all 0.5s ease;
        }

        .result.show {
            opacity: 1;
            transform: translateY(0);
        }

        .result.correct {
            background: rgba(76, 175, 80, 0.2);
            border: 2px solid #4CAF50;
        }

        .result.wrong {
            background: rgba(244, 67, 54, 0.2);
            border: 2px solid #f44336;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }

        .interactive-hint {
            text-align: center;
            color: rgba(255,255,255,0.7);
            font-size: 0.9rem;
            margin-top: 10px;
            animation: bounce 2s infinite;
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
            40% { transform: translateY(-10px); }
            60% { transform: translateY(-5px); }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">🖥️ 计算机性能测试</h1>
            <p class="subtitle">通过动画和交互学习基准测试概念</p>
        </div>

        <div class="section">
            <h2 class="section-title">💡 什么是基准测试？</h2>
            <div class="canvas-container">
                <canvas id="conceptCanvas" width="800" height="400"></canvas>
            </div>
            <p class="interactive-hint">点击画布查看动画演示</p>
            <div class="explanation">
                <strong>基准测试（Benchmark Test）</strong>是指把应用程序中应用最频繁的那部分核心程序作为评价计算机性能的标准程序。
                就像体检时测量身高体重一样，基准测试是衡量计算机"健康状况"的标准工具。
            </div>
        </div>

        <div class="section">
            <h2 class="section-title">🔄 基准测试工作原理</h2>
            <div class="canvas-container">
                <canvas id="processCanvas" width="800" height="400"></canvas>
            </div>
            <p class="interactive-hint">点击画布观看测试流程</p>
            <div class="explanation">
                基准测试通过运行标准化的程序来测试计算机的各项性能指标，包括：
                <br>• CPU运算能力 • 内存读写速度 • 磁盘I/O性能 • 网络传输能力
            </div>
        </div>

        <div class="section quiz-section">
            <h2 class="section-title">🎯 现在来做题吧！</h2>
            <div class="canvas-container">
                <canvas id="quizCanvas" width="800" height="300"></canvas>
            </div>
            
            <h3 style="text-align: center; margin: 30px 0; font-size: 1.3rem;">
                把应用程序中应用最频繁的那部分核心程序作为评价计算机性能的标准程序，称为（）程序。
            </h3>
            
            <div class="options">
                <div class="option" data-answer="A">A. 仿真测试</div>
                <div class="option" data-answer="B">B. 核心测试</div>
                <div class="option" data-answer="C">C. 基准测试</div>
                <div class="option" data-answer="D">D. 标准测试</div>
            </div>
            
            <div class="result" id="result"></div>
        </div>
    </div>

    <script>
        // 概念演示动画
        const conceptCanvas = document.getElementById('conceptCanvas');
        const conceptCtx = conceptCanvas.getContext('2d');
        let conceptAnimationFrame = 0;
        let conceptAnimating = false;

        function drawConcept() {
            conceptCtx.clearRect(0, 0, conceptCanvas.width, conceptCanvas.height);
            
            // 背景渐变
            const gradient = conceptCtx.createLinearGradient(0, 0, conceptCanvas.width, conceptCanvas.height);
            gradient.addColorStop(0, '#f8f9fa');
            gradient.addColorStop(1, '#e9ecef');
            conceptCtx.fillStyle = gradient;
            conceptCtx.fillRect(0, 0, conceptCanvas.width, conceptCanvas.height);
            
            if (conceptAnimating) {
                // 计算机图标
                conceptCtx.fillStyle = '#667eea';
                conceptCtx.fillRect(100, 150, 120, 80);
                conceptCtx.fillStyle = '#495057';
                conceptCtx.fillRect(110, 160, 100, 60);
                
                // 程序块动画
                const programs = [
                    {x: 300, y: 100, name: '文档处理', freq: 20},
                    {x: 300, y: 150, name: '图像渲染', freq: 60},
                    {x: 300, y: 200, name: '数据计算', freq: 80},
                    {x: 300, y: 250, name: '网络通信', freq: 40}
                ];
                
                programs.forEach((prog, i) => {
                    const alpha = Math.sin(conceptAnimationFrame * 0.1 + i) * 0.3 + 0.7;
                    const scale = prog.freq / 100;
                    
                    conceptCtx.save();
                    conceptCtx.globalAlpha = alpha;
                    conceptCtx.fillStyle = prog.freq > 70 ? '#28a745' : '#6c757d';
                    conceptCtx.fillRect(prog.x, prog.y, 150 * scale, 30);
                    
                    conceptCtx.fillStyle = 'white';
                    conceptCtx.font = '12px Arial';
                    conceptCtx.fillText(prog.name, prog.x + 10, prog.y + 20);
                    conceptCtx.restore();
                });
                
                // 箭头指向最频繁的程序
                if (conceptAnimationFrame > 60) {
                    conceptCtx.strokeStyle = '#dc3545';
                    conceptCtx.lineWidth = 3;
                    conceptCtx.beginPath();
                    conceptCtx.moveTo(500, 180);
                    conceptCtx.lineTo(550, 180);
                    conceptCtx.lineTo(540, 170);
                    conceptCtx.moveTo(550, 180);
                    conceptCtx.lineTo(540, 190);
                    conceptCtx.stroke();
                    
                    conceptCtx.fillStyle = '#dc3545';
                    conceptCtx.font = 'bold 16px Arial';
                    conceptCtx.fillText('基准测试程序', 560, 185);
                }
                
                conceptAnimationFrame++;
                if (conceptAnimationFrame < 120) {
                    requestAnimationFrame(drawConcept);
                } else {
                    conceptAnimating = false;
                }
            } else {
                // 静态显示
                conceptCtx.fillStyle = '#667eea';
                conceptCtx.font = 'bold 24px Arial';
                conceptCtx.textAlign = 'center';
                conceptCtx.fillText('点击开始动画演示', conceptCanvas.width/2, conceptCanvas.height/2);
            }
        }

        conceptCanvas.addEventListener('click', () => {
            if (!conceptAnimating) {
                conceptAnimating = true;
                conceptAnimationFrame = 0;
                drawConcept();
            }
        });

        // 流程演示动画
        const processCanvas = document.getElementById('processCanvas');
        const processCtx = processCanvas.getContext('2d');
        let processAnimationFrame = 0;
        let processAnimating = false;

        function drawProcess() {
            processCtx.clearRect(0, 0, processCanvas.width, processCanvas.height);
            
            const gradient = processCtx.createLinearGradient(0, 0, processCanvas.width, processCanvas.height);
            gradient.addColorStop(0, '#f8f9fa');
            gradient.addColorStop(1, '#e9ecef');
            processCtx.fillStyle = gradient;
            processCtx.fillRect(0, 0, processCanvas.width, processCanvas.height);
            
            if (processAnimating) {
                const steps = [
                    {x: 50, y: 200, text: '选择基准\n程序', color: '#667eea'},
                    {x: 200, y: 200, text: '运行测试', color: '#28a745'},
                    {x: 350, y: 200, text: '收集数据', color: '#ffc107'},
                    {x: 500, y: 200, text: '分析结果', color: '#dc3545'},
                    {x: 650, y: 200, text: '性能评估', color: '#6f42c1'}
                ];
                
                steps.forEach((step, i) => {
                    if (processAnimationFrame > i * 30) {
                        const alpha = Math.min((processAnimationFrame - i * 30) / 30, 1);
                        processCtx.save();
                        processCtx.globalAlpha = alpha;
                        
                        // 圆形背景
                        processCtx.fillStyle = step.color;
                        processCtx.beginPath();
                        processCtx.arc(step.x, step.y, 40, 0, Math.PI * 2);
                        processCtx.fill();
                        
                        // 文字
                        processCtx.fillStyle = 'white';
                        processCtx.font = 'bold 12px Arial';
                        processCtx.textAlign = 'center';
                        const lines = step.text.split('\n');
                        lines.forEach((line, lineIndex) => {
                            processCtx.fillText(line, step.x, step.y - 5 + lineIndex * 15);
                        });
                        
                        // 箭头
                        if (i < steps.length - 1 && processAnimationFrame > (i + 1) * 30) {
                            processCtx.strokeStyle = '#6c757d';
                            processCtx.lineWidth = 2;
                            processCtx.beginPath();
                            processCtx.moveTo(step.x + 40, step.y);
                            processCtx.lineTo(steps[i + 1].x - 40, steps[i + 1].y);
                            processCtx.stroke();
                        }
                        
                        processCtx.restore();
                    }
                });
                
                processAnimationFrame++;
                if (processAnimationFrame < 200) {
                    requestAnimationFrame(drawProcess);
                } else {
                    processAnimating = false;
                }
            } else {
                processCtx.fillStyle = '#667eea';
                processCtx.font = 'bold 24px Arial';
                processCtx.textAlign = 'center';
                processCtx.fillText('点击查看测试流程', processCanvas.width/2, processCanvas.height/2);
            }
        }

        processCanvas.addEventListener('click', () => {
            if (!processAnimating) {
                processAnimating = true;
                processAnimationFrame = 0;
                drawProcess();
            }
        });

        // 题目动画
        const quizCanvas = document.getElementById('quizCanvas');
        const quizCtx = quizCanvas.getContext('2d');
        let quizFrame = 0;

        function drawQuiz() {
            quizCtx.clearRect(0, 0, quizCanvas.width, quizCanvas.height);
            
            // 动态背景
            const time = Date.now() * 0.001;
            for (let i = 0; i < 50; i++) {
                const x = (i * 137.5) % quizCanvas.width;
                const y = Math.sin(time + i * 0.5) * 50 + quizCanvas.height / 2;
                const alpha = Math.sin(time + i * 0.3) * 0.3 + 0.4;
                
                quizCtx.save();
                quizCtx.globalAlpha = alpha;
                quizCtx.fillStyle = 'rgba(255, 255, 255, 0.1)';
                quizCtx.beginPath();
                quizCtx.arc(x, y, 3, 0, Math.PI * 2);
                quizCtx.fill();
                quizCtx.restore();
            }
            
            // 中心图标
            quizCtx.fillStyle = 'rgba(255, 255, 255, 0.8)';
            quizCtx.font = 'bold 48px Arial';
            quizCtx.textAlign = 'center';
            quizCtx.fillText('🎯', quizCanvas.width/2, quizCanvas.height/2 + 15);
            
            requestAnimationFrame(drawQuiz);
        }

        // 选项点击处理
        const options = document.querySelectorAll('.option');
        const result = document.getElementById('result');
        let answered = false;

        options.forEach(option => {
            option.addEventListener('click', () => {
                if (answered) return;
                
                answered = true;
                const userAnswer = option.dataset.answer;
                const correctAnswer = 'C';
                
                options.forEach(opt => {
                    if (opt.dataset.answer === correctAnswer) {
                        opt.classList.add('correct');
                    } else if (opt === option && userAnswer !== correctAnswer) {
                        opt.classList.add('wrong');
                    }
                    opt.style.pointerEvents = 'none';
                });
                
                setTimeout(() => {
                    if (userAnswer === correctAnswer) {
                        result.innerHTML = `
                            <div style="font-size: 2rem; margin-bottom: 10px;">🎉 恭喜答对了！</div>
                            <div>基准测试是评价计算机性能的标准程序，通过运行最频繁使用的核心程序来测试性能。</div>
                        `;
                        result.className = 'result correct show';
                    } else {
                        result.innerHTML = `
                            <div style="font-size: 2rem; margin-bottom: 10px;">❌ 答错了，再想想</div>
                            <div>正确答案是C。基准测试专门指那些最频繁使用的核心程序，用来作为性能评估的标准。</div>
                        `;
                        result.className = 'result wrong show';
                    }
                }, 500);
            });
        });

        // 初始化画布
        drawConcept();
        drawProcess();
        drawQuiz();
    </script>
</body>
</html>
