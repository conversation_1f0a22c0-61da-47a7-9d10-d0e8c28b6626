<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>网络子网划分 - 交互式学习</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .section {
            background: rgba(255, 255, 255, 0.95);
            margin: 30px 0;
            padding: 40px;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            opacity: 0;
            transform: translateY(50px);
        }

        .section.active {
            opacity: 1;
            transform: translateY(0);
        }

        h1 {
            font-size: 2.5em;
            text-align: center;
            color: #4a5568;
            margin-bottom: 30px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
        }

        h2 {
            font-size: 2em;
            color: #2d3748;
            margin-bottom: 20px;
            border-left: 5px solid #667eea;
            padding-left: 15px;
        }

        .question-box {
            background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
            padding: 30px;
            border-radius: 15px;
            margin: 20px 0;
            font-size: 1.2em;
            line-height: 1.8;
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
        }

        .canvas-container {
            display: flex;
            justify-content: center;
            margin: 30px 0;
            background: #f7fafc;
            padding: 20px;
            border-radius: 15px;
            border: 2px dashed #cbd5e0;
        }

        canvas {
            border: 2px solid #e2e8f0;
            border-radius: 10px;
            background: white;
            cursor: pointer;
        }

        .interactive-panel {
            background: #edf2f7;
            padding: 25px;
            border-radius: 15px;
            margin: 20px 0;
            text-align: center;
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1.1em;
            cursor: pointer;
            margin: 10px;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
        }

        .btn:active {
            transform: translateY(0);
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e2e8f0;
            border-radius: 4px;
            margin: 20px 0;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            width: 0%;
            transition: width 0.5s ease;
        }

        .step-indicator {
            display: flex;
            justify-content: center;
            margin: 30px 0;
        }

        .step {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #e2e8f0;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 10px;
            font-weight: bold;
            transition: all 0.3s ease;
        }

        .step.active {
            background: #667eea;
            color: white;
            transform: scale(1.2);
        }

        .step.completed {
            background: #48bb78;
            color: white;
        }

        .binary-display {
            font-family: 'Courier New', monospace;
            font-size: 1.5em;
            background: #2d3748;
            color: #68d391;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            margin: 20px 0;
            letter-spacing: 3px;
        }

        .highlight {
            background: #ffd700;
            padding: 2px 5px;
            border-radius: 3px;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }

        .navigation {
            position: fixed;
            bottom: 30px;
            right: 30px;
            z-index: 1000;
        }

        .nav-btn {
            background: rgba(102, 126, 234, 0.9);
            color: white;
            border: none;
            padding: 15px;
            border-radius: 50%;
            font-size: 1.2em;
            cursor: pointer;
            margin: 5px;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        .nav-btn:hover {
            background: rgba(102, 126, 234, 1);
            transform: scale(1.1);
        }

        .tooltip {
            position: relative;
            display: inline-block;
        }

        .tooltip .tooltiptext {
            visibility: hidden;
            width: 200px;
            background-color: #2d3748;
            color: white;
            text-align: center;
            border-radius: 6px;
            padding: 10px;
            position: absolute;
            z-index: 1;
            bottom: 125%;
            left: 50%;
            margin-left: -100px;
            opacity: 0;
            transition: opacity 0.3s;
            font-size: 0.9em;
        }

        .tooltip:hover .tooltiptext {
            visibility: visible;
            opacity: 1;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🌐 网络子网划分 - 从零开始学习</h1>
        
        <div class="progress-bar">
            <div class="progress-fill" id="progressFill"></div>
        </div>

        <div class="step-indicator">
            <div class="step active" id="step1">1</div>
            <div class="step" id="step2">2</div>
            <div class="step" id="step3">3</div>
            <div class="step" id="step4">4</div>
            <div class="step" id="step5">5</div>
        </div>

        <!-- 第一部分：题目展示 -->
        <div class="section active" id="section1">
            <h2>📝 原题目</h2>
            <div class="question-box">
                <strong>题目：</strong>分配给某公司网络的地址块是*************/20，该网络可以被划分为（ ）个C类子网。<br><br>
                <strong>选项：</strong><br>
                A. 4 &nbsp;&nbsp; B. 8 &nbsp;&nbsp; C. 16 &nbsp;&nbsp; D. 32<br><br>
                <strong>正确答案：</strong>C (16个)
            </div>
            <div class="interactive-panel">
                <p>🤔 看到这道题是不是感觉很困惑？别担心，我们从最基础的概念开始学习！</p>
                <button class="btn" onclick="nextSection()">开始学习 →</button>
            </div>
        </div>

        <!-- 第二部分：IP地址基础 -->
        <div class="section" id="section2">
            <h2>🔍 第一步：理解IP地址</h2>
            <div class="canvas-container">
                <canvas id="ipCanvas" width="800" height="400"></canvas>
            </div>
            <div class="interactive-panel">
                <p>IP地址就像门牌号，帮助数据找到正确的目的地</p>
                <button class="btn" onclick="animateIP()">点击看IP地址结构</button>
                <button class="btn" onclick="nextSection()">理解了，继续 →</button>
            </div>
        </div>

        <!-- 第三部分：子网掩码概念 -->
        <div class="section" id="section3">
            <h2>🎭 第二步：什么是子网掩码？</h2>
            <div class="canvas-container">
                <canvas id="maskCanvas" width="800" height="500"></canvas>
            </div>
            <div class="interactive-panel">
                <p>子网掩码就像一个"筛子"，帮我们区分网络部分和主机部分</p>
                <button class="btn" onclick="animateMask()">演示子网掩码工作原理</button>
                <button class="btn" onclick="nextSection()">明白了，继续 →</button>
            </div>
        </div>

        <!-- 第四部分：CIDR表示法 -->
        <div class="section" id="section4">
            <h2>📏 第三步：理解 /20 的含义</h2>
            <div class="binary-display" id="cidrDisplay">
                *************/20 = ?
            </div>
            <div class="canvas-container">
                <canvas id="cidrCanvas" width="800" height="400"></canvas>
            </div>
            <div class="interactive-panel">
                <p>/20 表示前20位是网络位，后12位是主机位</p>
                <button class="btn" onclick="animateCIDR()">可视化CIDR</button>
                <button class="btn" onclick="nextSection()">理解了，继续 →</button>
            </div>
        </div>

        <!-- 第五部分：计算过程 -->
        <div class="section" id="section5">
            <h2>🧮 第四步：计算子网数量</h2>
            <div class="canvas-container">
                <canvas id="calcCanvas" width="800" height="600"></canvas>
            </div>
            <div class="interactive-panel">
                <p>现在我们来一步步计算可以划分多少个C类子网</p>
                <button class="btn" onclick="startCalculation()">开始计算演示</button>
                <button class="btn" onclick="showAnswer()">查看最终答案</button>
            </div>
            <div id="answerBox" style="display: none; margin-top: 20px; padding: 20px; background: #d4edda; border-radius: 10px;">
                <h3>🎉 答案解析</h3>
                <p><strong>正确答案：C (16个)</strong></p>
                <p>因为C类网络的子网掩码是/24，而我们有/20的网络</p>
                <p>可用的子网位数 = 24 - 20 = 4位</p>
                <p>2^4 = 16个C类子网</p>
            </div>
        </div>
    </div>

    <div class="navigation">
        <button class="nav-btn" onclick="prevSection()">←</button>
        <button class="nav-btn" onclick="nextSection()">→</button>
    </div>

    <script>
        let currentSection = 1;
        const totalSections = 5;
        let ipAnimationPlayed = false;
        let maskAnimationPlayed = false;
        let cidrAnimationPlayed = false;
        let calcAnimationPlayed = false;

        // 初始化
        function init() {
            updateProgress();
            setupCanvas();
        }

        // 更新进度条
        function updateProgress() {
            const progress = (currentSection / totalSections) * 100;
            document.getElementById('progressFill').style.width = progress + '%';
            
            // 更新步骤指示器
            for (let i = 1; i <= totalSections; i++) {
                const step = document.getElementById(`step${i}`);
                step.classList.remove('active', 'completed');
                if (i < currentSection) {
                    step.classList.add('completed');
                } else if (i === currentSection) {
                    step.classList.add('active');
                }
            }
        }

        // 下一节
        function nextSection() {
            if (currentSection < totalSections) {
                document.getElementById(`section${currentSection}`).classList.remove('active');
                currentSection++;
                
                setTimeout(() => {
                    document.getElementById(`section${currentSection}`).classList.add('active');
                    gsap.fromTo(`#section${currentSection}`, 
                        { opacity: 0, y: 50 }, 
                        { opacity: 1, y: 0, duration: 0.8, ease: "power2.out" }
                    );
                }, 300);
                
                updateProgress();
            }
        }

        // 上一节
        function prevSection() {
            if (currentSection > 1) {
                document.getElementById(`section${currentSection}`).classList.remove('active');
                currentSection--;
                
                setTimeout(() => {
                    document.getElementById(`section${currentSection}`).classList.add('active');
                    gsap.fromTo(`#section${currentSection}`, 
                        { opacity: 0, y: -50 }, 
                        { opacity: 1, y: 0, duration: 0.8, ease: "power2.out" }
                    );
                }, 300);
                
                updateProgress();
            }
        }

        // 设置Canvas
        function setupCanvas() {
            const canvas = document.getElementById('ipCanvas');
            if (canvas) {
                const ctx = canvas.getContext('2d');
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                drawIPStructure(ctx);
            }

            const maskCanvas = document.getElementById('maskCanvas');
            if (maskCanvas) {
                const ctx = maskCanvas.getContext('2d');
                ctx.clearRect(0, 0, maskCanvas.width, maskCanvas.height);
                drawMaskStructure(ctx);
            }

            const cidrCanvas = document.getElementById('cidrCanvas');
            if (cidrCanvas) {
                const ctx = cidrCanvas.getContext('2d');
                ctx.clearRect(0, 0, cidrCanvas.width, cidrCanvas.height);
                drawCIDRStructure(ctx);
            }

            const calcCanvas = document.getElementById('calcCanvas');
            if (calcCanvas) {
                const ctx = calcCanvas.getContext('2d');
                ctx.clearRect(0, 0, calcCanvas.width, calcCanvas.height);
                drawCalculationStructure(ctx);
            }
        }

        // 绘制IP地址结构
        function drawIPStructure(ctx) {
            ctx.font = '24px Arial';
            ctx.textAlign = 'center';
            
            // 标题
            ctx.fillStyle = '#2d3748';
            ctx.fillText('IP地址：*************', 400, 50);
            
            // 绘制四个部分的框
            const boxWidth = 120;
            const boxHeight = 80;
            const startX = 100;
            const y = 120;
            
            const parts = ['210', '115', '192', '0'];
            const colors = ['#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4'];
            
            for (let i = 0; i < 4; i++) {
                const x = startX + i * (boxWidth + 40);
                
                // 绘制框
                ctx.fillStyle = colors[i];
                ctx.fillRect(x, y, boxWidth, boxHeight);
                
                // 绘制边框
                ctx.strokeStyle = '#2d3748';
                ctx.lineWidth = 2;
                ctx.strokeRect(x, y, boxWidth, boxHeight);
                
                // 绘制数字
                ctx.fillStyle = 'white';
                ctx.font = 'bold 28px Arial';
                ctx.fillText(parts[i], x + boxWidth/2, y + boxHeight/2 + 10);
                
                // 绘制点号
                if (i < 3) {
                    ctx.fillStyle = '#2d3748';
                    ctx.font = 'bold 36px Arial';
                    ctx.fillText('.', x + boxWidth + 20, y + boxHeight/2 + 10);
                }
            }
            
            // 说明文字
            ctx.fillStyle = '#666';
            ctx.font = '16px Arial';
            ctx.fillText('每个部分都是0-255之间的数字', 400, 250);
            ctx.fillText('总共32位二进制数，每部分8位', 400, 280);
        }

        // IP地址动画
        function animateIP() {
            if (ipAnimationPlayed) return;
            ipAnimationPlayed = true;

            const canvas = document.getElementById('ipCanvas');
            const ctx = canvas.getContext('2d');

            // 清空并重绘
            setupCanvas();

            // 添加二进制显示动画
            setTimeout(() => {
                ctx.fillStyle = '#e2e8f0';
                ctx.fillRect(50, 320, 700, 60);

                ctx.fillStyle = '#2d3748';
                ctx.font = '18px Courier New';
                ctx.textAlign = 'left';
                ctx.fillText('二进制表示:', 60, 340);

                const binary = '11010010.01110011.11000000.00000000';
                let index = 0;

                const typeWriter = setInterval(() => {
                    if (index < binary.length) {
                        ctx.fillStyle = '#48bb78';
                        ctx.fillText(binary.charAt(index), 200 + index * 12, 360);
                        index++;
                    } else {
                        clearInterval(typeWriter);
                    }
                }, 100);
            }, 1000);
        }

        // 绘制子网掩码结构
        function drawMaskStructure(ctx) {
            ctx.font = '20px Arial';
            ctx.textAlign = 'center';

            // 标题
            ctx.fillStyle = '#2d3748';
            ctx.fillText('子网掩码的作用', 400, 40);

            // IP地址
            ctx.font = '16px Arial';
            ctx.fillText('IP地址: *************', 200, 80);
            ctx.fillText('子网掩码: *************', 600, 80);

            // 绘制二进制对比
            const ipBinary = '11010010 01110011 11000000 00000000';
            const maskBinary = '11111111 11111111 11110000 00000000';

            ctx.font = '14px Courier New';
            ctx.textAlign = 'left';

            // IP二进制
            ctx.fillStyle = '#4299e1';
            ctx.fillText('IP:   ' + ipBinary, 50, 150);

            // 掩码二进制
            ctx.fillStyle = '#ed8936';
            ctx.fillText('掩码: ' + maskBinary, 50, 180);

            // 结果
            ctx.fillStyle = '#38a169';
            ctx.fillText('网络: 11010010 01110011 11000000 00000000', 50, 220);

            // 说明
            ctx.font = '16px Arial';
            ctx.textAlign = 'center';
            ctx.fillStyle = '#666';
            ctx.fillText('掩码中的1表示网络位，0表示主机位', 400, 280);
            ctx.fillText('通过AND运算得到网络地址', 400, 310);
        }

        // 子网掩码动画
        function animateMask() {
            if (maskAnimationPlayed) return;
            maskAnimationPlayed = true;

            const canvas = document.getElementById('maskCanvas');
            const ctx = canvas.getContext('2d');

            drawMaskStructure(ctx);

            // 添加高亮动画
            setTimeout(() => {
                let step = 0;
                const highlightInterval = setInterval(() => {
                    if (step < 4) {
                        // 高亮不同的字节
                        ctx.fillStyle = 'rgba(255, 215, 0, 0.5)';
                        ctx.fillRect(130 + step * 72, 135, 65, 20);
                        ctx.fillRect(130 + step * 72, 165, 65, 20);
                        step++;
                    } else {
                        clearInterval(highlightInterval);
                    }
                }, 800);
            }, 500);
        }

        // 绘制CIDR结构
        function drawCIDRStructure(ctx) {
            ctx.font = '20px Arial';
            ctx.textAlign = 'center';
            ctx.fillStyle = '#2d3748';
            ctx.fillText('/20 表示前20位是网络位', 400, 40);

            // 绘制32位示意图
            const bitWidth = 20;
            const startX = 50;
            const y = 100;

            for (let i = 0; i < 32; i++) {
                const x = startX + i * bitWidth;

                if (i < 20) {
                    // 网络位 - 红色
                    ctx.fillStyle = '#e53e3e';
                } else {
                    // 主机位 - 蓝色
                    ctx.fillStyle = '#3182ce';
                }

                ctx.fillRect(x, y, bitWidth - 1, 40);

                // 绘制位号
                ctx.fillStyle = 'white';
                ctx.font = '10px Arial';
                ctx.fillText((i + 1).toString(), x + bitWidth/2, y + 25);
            }

            // 标签
            ctx.fillStyle = '#e53e3e';
            ctx.font = '16px Arial';
            ctx.fillText('网络位 (20位)', 200, 180);

            ctx.fillStyle = '#3182ce';
            ctx.fillText('主机位 (12位)', 600, 180);

            // 说明
            ctx.fillStyle = '#666';
            ctx.font = '14px Arial';
            ctx.fillText('C类网络是/24，我们的网络是/20', 400, 220);
            ctx.fillText('所以我们可以在第21-24位创建子网', 400, 240);
        }

        // CIDR动画
        function animateCIDR() {
            if (cidrAnimationPlayed) return;
            cidrAnimationPlayed = true;

            const display = document.getElementById('cidrDisplay');
            const steps = [
                '*************/20 = ?',
                '前20位是网络位',
                '后12位是主机位',
                'C类网络是/24',
                '可用子网位 = 24 - 20 = 4位'
            ];

            let step = 0;
            const interval = setInterval(() => {
                if (step < steps.length) {
                    display.textContent = steps[step];
                    step++;
                } else {
                    clearInterval(interval);
                }
            }, 2000);

            drawCIDRStructure(document.getElementById('cidrCanvas').getContext('2d'));
        }

        // 绘制计算结构
        function drawCalculationStructure(ctx) {
            ctx.font = '18px Arial';
            ctx.textAlign = 'center';
            ctx.fillStyle = '#2d3748';
            ctx.fillText('子网计算步骤', 400, 40);

            // 步骤框
            const steps = [
                '1. 确定网络类型',
                '2. 计算可用位数',
                '3. 计算子网数量',
                '4. 验证答案'
            ];

            for (let i = 0; i < steps.length; i++) {
                const y = 80 + i * 120;

                // 绘制步骤框
                ctx.fillStyle = '#e2e8f0';
                ctx.fillRect(100, y, 600, 80);

                ctx.strokeStyle = '#cbd5e0';
                ctx.lineWidth = 2;
                ctx.strokeRect(100, y, 600, 80);

                // 步骤文字
                ctx.fillStyle = '#2d3748';
                ctx.font = '16px Arial';
                ctx.fillText(steps[i], 400, y + 30);

                // 步骤编号
                ctx.fillStyle = '#667eea';
                ctx.beginPath();
                ctx.arc(80, y + 40, 20, 0, 2 * Math.PI);
                ctx.fill();

                ctx.fillStyle = 'white';
                ctx.font = 'bold 16px Arial';
                ctx.fillText((i + 1).toString(), 80, y + 45);
            }
        }

        // 开始计算演示
        function startCalculation() {
            if (calcAnimationPlayed) return;
            calcAnimationPlayed = true;

            const canvas = document.getElementById('calcCanvas');
            const ctx = canvas.getContext('2d');

            drawCalculationStructure(ctx);

            const details = [
                'C类网络 = /24',
                '我们的网络 = /20',
                '可用位数 = 24 - 20 = 4位',
                '子网数 = 2^4 = 16个'
            ];

            let step = 0;
            const interval = setInterval(() => {
                if (step < details.length) {
                    const y = 80 + step * 120;

                    // 高亮当前步骤
                    ctx.fillStyle = 'rgba(102, 126, 234, 0.3)';
                    ctx.fillRect(100, y, 600, 80);

                    // 添加详细说明
                    ctx.fillStyle = '#48bb78';
                    ctx.font = '14px Arial';
                    ctx.fillText(details[step], 400, y + 55);

                    step++;
                } else {
                    clearInterval(interval);
                }
            }, 1500);
        }

        // 显示答案
        function showAnswer() {
            document.getElementById('answerBox').style.display = 'block';
            gsap.fromTo('#answerBox',
                { opacity: 0, y: 30 },
                { opacity: 1, y: 0, duration: 0.8, ease: "power2.out" }
            );
        }

        // 页面加载完成后初始化
        window.addEventListener('load', init);

        // GSAP动画增强
        gsap.registerPlugin();

        // 入场动画
        gsap.fromTo('.section.active',
            { opacity: 0, y: 50 },
            { opacity: 1, y: 0, duration: 1, ease: "power2.out" }
        );
    </script>
</body>
</html>
