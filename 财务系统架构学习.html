<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>财务系统架构方案对比学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            color: white;
            margin-bottom: 40px;
            animation: fadeInDown 1s ease-out;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .section {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            opacity: 0;
            transform: translateY(50px);
            animation: slideUp 0.8s ease-out forwards;
        }

        .section:nth-child(2) { animation-delay: 0.2s; }
        .section:nth-child(3) { animation-delay: 0.4s; }
        .section:nth-child(4) { animation-delay: 0.6s; }

        .section-title {
            font-size: 1.8em;
            color: #333;
            margin-bottom: 20px;
            text-align: center;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 2px;
        }

        .canvas-container {
            display: flex;
            justify-content: center;
            margin: 20px 0;
        }

        canvas {
            border: 2px solid #e0e0e0;
            border-radius: 15px;
            background: white;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .controls {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin: 20px 0;
            flex-wrap: wrap;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 25px;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .btn-primary {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.6);
        }

        .btn-secondary {
            background: linear-gradient(45deg, #f093fb, #f5576c);
            color: white;
            box-shadow: 0 4px 15px rgba(245, 87, 108, 0.4);
        }

        .btn-secondary:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(245, 87, 108, 0.6);
        }

        .explanation {
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            border-radius: 15px;
            padding: 25px;
            margin: 20px 0;
            border-left: 5px solid #667eea;
            font-size: 16px;
            line-height: 1.6;
        }

        .highlight {
            background: linear-gradient(120deg, #a8edea 0%, #fed6e3 100%);
            padding: 3px 8px;
            border-radius: 5px;
            font-weight: 600;
        }

        .quiz-section {
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
            border-radius: 20px;
            padding: 30px;
            margin: 30px 0;
        }

        .quiz-title {
            font-size: 1.5em;
            color: #d63031;
            margin-bottom: 20px;
            text-align: center;
        }

        .options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }

        .option {
            background: white;
            border: 2px solid #e0e0e0;
            border-radius: 10px;
            padding: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
        }

        .option:hover {
            border-color: #667eea;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .option.correct {
            border-color: #00b894;
            background: linear-gradient(135deg, #00b894, #55efc4);
            color: white;
        }

        .option.wrong {
            border-color: #e17055;
            background: linear-gradient(135deg, #e17055, #fdcb6e);
            color: white;
        }

        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes slideUp {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .pulse {
            animation: pulse 2s infinite;
        }

        .step-indicator {
            display: flex;
            justify-content: center;
            margin: 20px 0;
            gap: 10px;
        }

        .step {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #e0e0e0;
            transition: all 0.3s ease;
        }

        .step.active {
            background: #667eea;
            transform: scale(1.2);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏫 财务系统架构方案学习</h1>
            <p>通过动画和交互理解网络架构设计</p>
        </div>

        <div class="section">
            <h2 class="section-title">📚 基础知识讲解</h2>
            <div class="canvas-container">
                <canvas id="knowledgeCanvas" width="800" height="400"></canvas>
            </div>
            <div class="controls">
                <button class="btn btn-primary" onclick="startKnowledgeAnimation()">开始学习</button>
                <button class="btn btn-secondary" onclick="resetKnowledge()">重置</button>
            </div>
            <div class="explanation">
                <h3>🎯 核心概念解释：</h3>
                <p><span class="highlight">防火墙</span>：网络安全设备，控制网络流量进出</p>
                <p><span class="highlight">地址映射</span>：将内网地址映射到外网，实现访问</p>
                <p><span class="highlight">虚拟化技术</span>：在一台物理服务器上运行多个虚拟机</p>
                <p><span class="highlight">业务连续性</span>：系统故障时能快速恢复，保持服务不中断</p>
            </div>
        </div>

        <div class="section">
            <h2 class="section-title">🔄 方案对比动画</h2>
            <div class="canvas-container">
                <canvas id="comparisonCanvas" width="800" height="500"></canvas>
            </div>
            <div class="controls">
                <button class="btn btn-primary" onclick="showScheme1()">方案一演示</button>
                <button class="btn btn-secondary" onclick="showScheme2()">方案二演示</button>
                <button class="btn btn-primary" onclick="compareSchemes()">对比分析</button>
            </div>
            <div class="step-indicator">
                <div class="step" id="step1"></div>
                <div class="step" id="step2"></div>
                <div class="step" id="step3"></div>
            </div>
        </div>

        <div class="quiz-section">
            <h2 class="quiz-title">🎯 题目解析与答题</h2>
            <div class="explanation">
                <h3>📝 题目分析：</h3>
                <p><strong>问题：</strong>与方案一相比，方案二的优点是什么？</p>
                <br>
                <h4>🔍 解题思路：</h4>
                <p>1. <span class="highlight">对比两个方案的核心差异</span></p>
                <p>2. <span class="highlight">分析虚拟化技术的优势</span></p>
                <p>3. <span class="highlight">理解业务连续性概念</span></p>
            </div>

            <div class="options">
                <div class="option" onclick="selectOption(this, false)">
                    <strong>A. 网络的安全性得到保障</strong>
                    <p>两方案网络架构相同，安全性无差异</p>
                </div>
                <div class="option" onclick="selectOption(this, false)">
                    <strong>B. 数据的安全性得到保障</strong>
                    <p>方案二缺少专门的备份服务器</p>
                </div>
                <div class="option" onclick="selectOption(this, true)">
                    <strong>C. 业务的连续性得到保障</strong>
                    <p>虚拟化技术支持故障时业务迁移</p>
                </div>
                <div class="option" onclick="selectOption(this, false)">
                    <strong>D. 业务的可用性得到保障</strong>
                    <p>可用性在两方案中基本相同</p>
                </div>
            </div>

            <div id="answerExplanation" style="display: none;" class="explanation">
                <h3>✅ 正确答案解析：</h3>
                <p><strong>答案：C - 业务的连续性得到保障</strong></p>
                <br>
                <p><span class="highlight">关键理解：</span></p>
                <p>• <strong>方案一</strong>：使用独立的物理服务器，一台故障会影响对应业务</p>
                <p>• <strong>方案二</strong>：采用虚拟化技术，故障时可将虚拟机迁移到另一台物理服务器</p>
                <p>• <strong>业务连续性</strong>：指系统在故障时能够快速恢复，保持服务不中断</p>
            </div>
        </div>
    </div>

    <script>
        // 知识讲解动画
        const knowledgeCanvas = document.getElementById('knowledgeCanvas');
        const knowledgeCtx = knowledgeCanvas.getContext('2d');
        let knowledgeStep = 0;
        let animationId;

        function drawRoundedRect(ctx, x, y, width, height, radius, fillColor, strokeColor) {
            ctx.beginPath();
            ctx.roundRect(x, y, width, height, radius);
            if (fillColor) {
                ctx.fillStyle = fillColor;
                ctx.fill();
            }
            if (strokeColor) {
                ctx.strokeStyle = strokeColor;
                ctx.lineWidth = 2;
                ctx.stroke();
            }
        }

        function drawServer(ctx, x, y, label, color) {
            // 服务器主体
            drawRoundedRect(ctx, x, y, 80, 60, 8, color, '#333');
            
            // 服务器细节
            ctx.fillStyle = '#333';
            ctx.fillRect(x + 10, y + 10, 60, 8);
            ctx.fillRect(x + 10, y + 25, 60, 8);
            ctx.fillRect(x + 10, y + 40, 60, 8);
            
            // 标签
            ctx.fillStyle = '#333';
            ctx.font = '12px Arial';
            ctx.textAlign = 'center';
            ctx.fillText(label, x + 40, y + 80);
        }

        function drawFirewall(ctx, x, y) {
            // 防火墙图标
            drawRoundedRect(ctx, x, y, 60, 40, 5, '#ff6b6b', '#333');
            
            // 防火墙标志
            ctx.fillStyle = 'white';
            ctx.font = 'bold 16px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('🔥', x + 30, y + 28);
            
            ctx.fillStyle = '#333';
            ctx.font = '12px Arial';
            ctx.fillText('防火墙', x + 30, y + 55);
        }

        function drawConnection(ctx, x1, y1, x2, y2, animated = false) {
            ctx.beginPath();
            ctx.moveTo(x1, y1);
            ctx.lineTo(x2, y2);
            ctx.strokeStyle = animated ? '#4ecdc4' : '#666';
            ctx.lineWidth = animated ? 3 : 2;
            ctx.stroke();
            
            if (animated) {
                // 绘制数据流动画
                const time = Date.now() * 0.005;
                const progress = (Math.sin(time) + 1) / 2;
                const dotX = x1 + (x2 - x1) * progress;
                const dotY = y1 + (y2 - y1) * progress;
                
                ctx.beginPath();
                ctx.arc(dotX, dotY, 4, 0, Math.PI * 2);
                ctx.fillStyle = '#4ecdc4';
                ctx.fill();
            }
        }

        function startKnowledgeAnimation() {
            knowledgeStep = 0;
            animateKnowledge();
        }

        function animateKnowledge() {
            knowledgeCtx.clearRect(0, 0, knowledgeCanvas.width, knowledgeCanvas.height);
            
            // 背景
            const gradient = knowledgeCtx.createLinearGradient(0, 0, 800, 400);
            gradient.addColorStop(0, '#f8f9fa');
            gradient.addColorStop(1, '#e9ecef');
            knowledgeCtx.fillStyle = gradient;
            knowledgeCtx.fillRect(0, 0, 800, 400);
            
            // 标题
            knowledgeCtx.fillStyle = '#333';
            knowledgeCtx.font = 'bold 20px Arial';
            knowledgeCtx.textAlign = 'center';
            knowledgeCtx.fillText('网络架构基础知识', 400, 30);
            
            if (knowledgeStep >= 1) {
                // 绘制校园网
                knowledgeCtx.fillStyle = '#74b9ff';
                knowledgeCtx.font = '16px Arial';
                knowledgeCtx.fillText('校园网用户', 100, 200);
                
                // 用户图标
                knowledgeCtx.beginPath();
                knowledgeCtx.arc(100, 220, 20, 0, Math.PI * 2);
                knowledgeCtx.fillStyle = '#0984e3';
                knowledgeCtx.fill();
                knowledgeCtx.fillStyle = 'white';
                knowledgeCtx.font = '16px Arial';
                knowledgeCtx.textAlign = 'center';
                knowledgeCtx.fillText('👤', 100, 228);
            }
            
            if (knowledgeStep >= 2) {
                // 绘制防火墙
                drawFirewall(knowledgeCtx, 300, 180);
                
                // 连接线
                drawConnection(knowledgeCtx, 120, 220, 300, 200, knowledgeStep >= 3);
            }
            
            if (knowledgeStep >= 4) {
                // 绘制服务器
                drawServer(knowledgeCtx, 500, 150, '财务服务器1', '#55a3ff');
                drawServer(knowledgeCtx, 600, 150, '财务服务器2', '#55a3ff');
                drawServer(knowledgeCtx, 550, 250, '备份服务器', '#ffa502');
                
                // 连接线
                drawConnection(knowledgeCtx, 360, 200, 500, 180, knowledgeStep >= 5);
                drawConnection(knowledgeCtx, 360, 200, 600, 180, knowledgeStep >= 5);
                drawConnection(knowledgeCtx, 360, 200, 550, 280, knowledgeStep >= 5);
            }
            
            knowledgeStep++;
            if (knowledgeStep <= 6) {
                setTimeout(() => animateKnowledge(), 1500);
            }
        }

        function resetKnowledge() {
            knowledgeStep = 0;
            knowledgeCtx.clearRect(0, 0, knowledgeCanvas.width, knowledgeCanvas.height);
        }

        // 方案对比动画
        const comparisonCanvas = document.getElementById('comparisonCanvas');
        const comparisonCtx = comparisonCanvas.getContext('2d');
        let currentScheme = 0;
        let comparisonStep = 0;

        function drawVirtualMachine(ctx, x, y, label, color, opacity = 1) {
            ctx.globalAlpha = opacity;
            drawRoundedRect(ctx, x, y, 60, 40, 5, color, '#333');
            ctx.fillStyle = 'white';
            ctx.font = '10px Arial';
            ctx.textAlign = 'center';
            ctx.fillText(label, x + 30, y + 25);
            ctx.globalAlpha = 1;
        }

        function showScheme1() {
            currentScheme = 1;
            comparisonStep = 0;
            document.getElementById('step1').classList.add('active');
            document.getElementById('step2').classList.remove('active');
            document.getElementById('step3').classList.remove('active');
            animateScheme1();
        }

        function animateScheme1() {
            comparisonCtx.clearRect(0, 0, comparisonCanvas.width, comparisonCanvas.height);

            // 背景
            const gradient = comparisonCtx.createLinearGradient(0, 0, 800, 500);
            gradient.addColorStop(0, '#f1f2f6');
            gradient.addColorStop(1, '#ddd');
            comparisonCtx.fillStyle = gradient;
            comparisonCtx.fillRect(0, 0, 800, 500);

            // 标题
            comparisonCtx.fillStyle = '#2d3436';
            comparisonCtx.font = 'bold 24px Arial';
            comparisonCtx.textAlign = 'center';
            comparisonCtx.fillText('方案一：传统物理服务器架构', 400, 40);

            if (comparisonStep >= 1) {
                // 校园网用户
                comparisonCtx.beginPath();
                comparisonCtx.arc(80, 250, 25, 0, Math.PI * 2);
                comparisonCtx.fillStyle = '#0984e3';
                comparisonCtx.fill();
                comparisonCtx.fillStyle = 'white';
                comparisonCtx.font = '20px Arial';
                comparisonCtx.fillText('👤', 80, 258);

                comparisonCtx.fillStyle = '#333';
                comparisonCtx.font = '14px Arial';
                comparisonCtx.fillText('校园网用户', 80, 290);
            }

            if (comparisonStep >= 2) {
                // 防火墙
                drawFirewall(comparisonCtx, 200, 230);
                drawConnection(comparisonCtx, 105, 250, 200, 250);
            }

            if (comparisonStep >= 3) {
                // 三台财务服务器
                drawServer(comparisonCtx, 350, 150, '财务服务器1', '#55a3ff');
                drawServer(comparisonCtx, 450, 150, '财务服务器2', '#55a3ff');
                drawServer(comparisonCtx, 550, 150, '财务服务器3', '#55a3ff');

                // 备份服务器
                drawServer(comparisonCtx, 450, 320, '备份服务器', '#ffa502');

                // 连接线
                drawConnection(comparisonCtx, 260, 250, 350, 180);
                drawConnection(comparisonCtx, 260, 250, 450, 180);
                drawConnection(comparisonCtx, 260, 250, 550, 180);

                // 备份连接
                drawConnection(comparisonCtx, 390, 210, 450, 320, true);
                drawConnection(comparisonCtx, 490, 210, 490, 320, true);
                drawConnection(comparisonCtx, 590, 210, 490, 320, true);
            }

            if (comparisonStep >= 4) {
                // 特点说明
                comparisonCtx.fillStyle = '#e17055';
                comparisonCtx.font = '16px Arial';
                comparisonCtx.textAlign = 'left';
                comparisonCtx.fillText('特点：', 50, 400);
                comparisonCtx.fillText('• 4台独立物理服务器', 50, 425);
                comparisonCtx.fillText('• 专门的备份服务器', 50, 450);
                comparisonCtx.fillText('• 服务器故障影响对应业务', 50, 475);
            }

            comparisonStep++;
            if (comparisonStep <= 5) {
                setTimeout(() => animateScheme1(), 1000);
            }
        }

        function showScheme2() {
            currentScheme = 2;
            comparisonStep = 0;
            document.getElementById('step1').classList.remove('active');
            document.getElementById('step2').classList.add('active');
            document.getElementById('step3').classList.remove('active');
            animateScheme2();
        }

        function animateScheme2() {
            comparisonCtx.clearRect(0, 0, comparisonCanvas.width, comparisonCanvas.height);

            // 背景
            const gradient = comparisonCtx.createLinearGradient(0, 0, 800, 500);
            gradient.addColorStop(0, '#f1f2f6');
            gradient.addColorStop(1, '#ddd');
            comparisonCtx.fillStyle = gradient;
            comparisonCtx.fillRect(0, 0, 800, 500);

            // 标题
            comparisonCtx.fillStyle = '#2d3436';
            comparisonCtx.font = 'bold 24px Arial';
            comparisonCtx.textAlign = 'center';
            comparisonCtx.fillText('方案二：虚拟化技术架构', 400, 40);

            if (comparisonStep >= 1) {
                // 校园网用户
                comparisonCtx.beginPath();
                comparisonCtx.arc(80, 250, 25, 0, Math.PI * 2);
                comparisonCtx.fillStyle = '#0984e3';
                comparisonCtx.fill();
                comparisonCtx.fillStyle = 'white';
                comparisonCtx.font = '20px Arial';
                comparisonCtx.fillText('👤', 80, 258);

                comparisonCtx.fillStyle = '#333';
                comparisonCtx.font = '14px Arial';
                comparisonCtx.fillText('校园网用户', 80, 290);
            }

            if (comparisonStep >= 2) {
                // 防火墙
                drawFirewall(comparisonCtx, 200, 230);
                drawConnection(comparisonCtx, 105, 250, 200, 250);
            }

            if (comparisonStep >= 3) {
                // 两台物理服务器
                drawServer(comparisonCtx, 400, 120, '物理服务器1', '#6c5ce7');
                drawServer(comparisonCtx, 500, 120, '物理服务器2', '#6c5ce7');

                // 连接线
                drawConnection(comparisonCtx, 260, 250, 400, 150);
                drawConnection(comparisonCtx, 260, 250, 500, 150);
            }

            if (comparisonStep >= 4) {
                // 虚拟机
                drawVirtualMachine(comparisonCtx, 350, 200, 'VM1', '#55a3ff');
                drawVirtualMachine(comparisonCtx, 420, 200, 'VM2', '#55a3ff');
                drawVirtualMachine(comparisonCtx, 490, 200, 'VM3', '#55a3ff');
                drawVirtualMachine(comparisonCtx, 560, 200, 'VM4', '#ffa502');

                // 虚拟机连接线
                drawConnection(comparisonCtx, 440, 180, 380, 200);
                drawConnection(comparisonCtx, 440, 180, 450, 200);
                drawConnection(comparisonCtx, 540, 180, 520, 200);
                drawConnection(comparisonCtx, 540, 180, 590, 200);
            }

            if (comparisonStep >= 5) {
                // 故障迁移演示
                comparisonCtx.fillStyle = '#e17055';
                comparisonCtx.font = 'bold 14px Arial';
                comparisonCtx.textAlign = 'center';
                comparisonCtx.fillText('故障时虚拟机可迁移', 450, 280);

                // 迁移箭头
                comparisonCtx.beginPath();
                comparisonCtx.moveTo(420, 240);
                comparisonCtx.lineTo(500, 240);
                comparisonCtx.strokeStyle = '#e17055';
                comparisonCtx.lineWidth = 3;
                comparisonCtx.stroke();

                // 箭头头部
                comparisonCtx.beginPath();
                comparisonCtx.moveTo(490, 235);
                comparisonCtx.lineTo(500, 240);
                comparisonCtx.lineTo(490, 245);
                comparisonCtx.stroke();
            }

            if (comparisonStep >= 6) {
                // 特点说明
                comparisonCtx.fillStyle = '#00b894';
                comparisonCtx.font = '16px Arial';
                comparisonCtx.textAlign = 'left';
                comparisonCtx.fillText('特点：', 50, 350);
                comparisonCtx.fillText('• 2台物理服务器', 50, 375);
                comparisonCtx.fillText('• 虚拟化技术', 50, 400);
                comparisonCtx.fillText('• 故障时可迁移虚拟机', 50, 425);
                comparisonCtx.fillText('• 保障业务连续性', 50, 450);
            }

            comparisonStep++;
            if (comparisonStep <= 7) {
                setTimeout(() => animateScheme2(), 1000);
            }
        }

        function compareSchemes() {
            document.getElementById('step1').classList.remove('active');
            document.getElementById('step2').classList.remove('active');
            document.getElementById('step3').classList.add('active');

            comparisonCtx.clearRect(0, 0, comparisonCanvas.width, comparisonCanvas.height);

            // 背景
            const gradient = comparisonCtx.createLinearGradient(0, 0, 800, 500);
            gradient.addColorStop(0, '#f8f9fa');
            gradient.addColorStop(1, '#e9ecef');
            comparisonCtx.fillStyle = gradient;
            comparisonCtx.fillRect(0, 0, 800, 500);

            // 标题
            comparisonCtx.fillStyle = '#2d3436';
            comparisonCtx.font = 'bold 24px Arial';
            comparisonCtx.textAlign = 'center';
            comparisonCtx.fillText('方案对比分析', 400, 40);

            // 分割线
            comparisonCtx.beginPath();
            comparisonCtx.moveTo(400, 70);
            comparisonCtx.lineTo(400, 450);
            comparisonCtx.strokeStyle = '#ddd';
            comparisonCtx.lineWidth = 2;
            comparisonCtx.stroke();

            // 方案一
            comparisonCtx.fillStyle = '#74b9ff';
            comparisonCtx.font = 'bold 18px Arial';
            comparisonCtx.textAlign = 'center';
            comparisonCtx.fillText('方案一', 200, 90);

            comparisonCtx.fillStyle = '#333';
            comparisonCtx.font = '14px Arial';
            comparisonCtx.textAlign = 'left';
            comparisonCtx.fillText('✓ 数据安全性好', 50, 120);
            comparisonCtx.fillText('✓ 专门备份服务器', 50, 145);
            comparisonCtx.fillText('✗ 故障影响业务', 50, 170);
            comparisonCtx.fillText('✗ 资源利用率低', 50, 195);

            // 方案二
            comparisonCtx.fillStyle = '#00b894';
            comparisonCtx.font = 'bold 18px Arial';
            comparisonCtx.textAlign = 'center';
            comparisonCtx.fillText('方案二', 600, 90);

            comparisonCtx.fillStyle = '#333';
            comparisonCtx.font = '14px Arial';
            comparisonCtx.textAlign = 'left';
            comparisonCtx.fillText('✓ 业务连续性好', 450, 120);
            comparisonCtx.fillText('✓ 资源利用率高', 450, 145);
            comparisonCtx.fillText('✓ 故障快速恢复', 450, 170);
            comparisonCtx.fillText('✗ 缺少专门备份', 450, 195);

            // 核心差异
            comparisonCtx.fillStyle = '#e17055';
            comparisonCtx.font = 'bold 16px Arial';
            comparisonCtx.textAlign = 'center';
            comparisonCtx.fillText('核心差异：虚拟化技术带来的业务连续性保障', 400, 250);

            // 连续性示意图
            comparisonCtx.fillStyle = '#fdcb6e';
            comparisonCtx.fillRect(150, 280, 500, 100);
            comparisonCtx.fillStyle = '#333';
            comparisonCtx.font = '14px Arial';
            comparisonCtx.textAlign = 'center';
            comparisonCtx.fillText('业务连续性 = 系统故障时能够快速恢复，保持服务不中断', 400, 320);
            comparisonCtx.fillText('方案二通过虚拟机迁移技术实现了这一点', 400, 345);
        }

        function selectOption(element, isCorrect) {
            // 清除所有选项的状态
            const options = document.querySelectorAll('.option');
            options.forEach(opt => {
                opt.classList.remove('correct', 'wrong');
            });

            // 设置当前选项状态
            if (isCorrect) {
                element.classList.add('correct');
                document.getElementById('answerExplanation').style.display = 'block';
            } else {
                element.classList.add('wrong');
                setTimeout(() => {
                    element.classList.remove('wrong');
                }, 2000);
            }
        }

        // 初始化
        resetKnowledge();
    </script>
</body>
</html>
